#!/usr/bin/env python3
"""
Test script to validate the feedback loop system improvements in GretahAI ScriptWeaver.

This script tests:
1. Adaptive frequency threshold in validation guidelines
2. Enhanced feedback tracking and metrics
3. Regeneration tracking
4. Improved prompt generation with feedback integration
5. Feedback effectiveness analysis
"""

import sys
import os
sys.path.append('.')

def test_feedback_loop_system():
    """Test the complete feedback loop system."""
    print("=" * 60)
    print("TESTING GRETAHAI SCRIPTWEAVER FEEDBACK LOOP SYSTEM")
    print("=" * 60)
    
    try:
        # Import required modules
        from state_manager import StateManager
        from core.prompt_builder import generate_enhanced_test_script_prompt, generate_validation_guidelines
        print("✓ Successfully imported required modules")
        
        # Create test state manager
        state = StateManager()
        print("✓ StateManager instance created")
        
        # Test 1: Add validation feedback entries
        print("\n1. Testing validation feedback collection...")
        validation_results = [
            {
                'quality_score': 65,
                'issues_found': [
                    {'category': 'locators', 'severity': 'medium', 'description': 'Using generic XPath selectors'},
                    {'category': 'waits', 'severity': 'high', 'description': 'Missing WebDriverWait conditions'}
                ],
                'recommendations': [
                    'Use CSS selectors instead of XPath',
                    'Add explicit wait conditions before interactions'
                ],
                'confidence_rating': 'medium',
                'ready_for_execution': True
            },
            {
                'quality_score': 70,
                'issues_found': [
                    {'category': 'locators', 'severity': 'medium', 'description': 'Using generic XPath selectors'},
                    {'category': 'assertions', 'severity': 'high', 'description': 'Missing meaningful assertions'}
                ],
                'recommendations': [
                    'Use CSS selectors instead of XPath',
                    'Add specific assertions for expected outcomes'
                ],
                'confidence_rating': 'medium',
                'ready_for_execution': True
            }
        ]
        
        for i, result in enumerate(validation_results):
            state.add_validation_feedback(result, f'TC00{i+1}', str(i+1))
        
        print(f"✓ Added {len(validation_results)} validation feedback entries")
        
        # Test 2: Common issues analysis with adaptive threshold
        print("\n2. Testing adaptive frequency threshold...")
        common_issues = state.get_common_validation_issues(limit=10)
        print(f"✓ Found {len(common_issues)} common issues")
        
        # Check if single frequency issues are included (should be with adaptive threshold)
        single_freq_issues = [issue for issue in common_issues if issue.get('frequency', 0) == 1]
        multiple_freq_issues = [issue for issue in common_issues if issue.get('frequency', 0) > 1]
        
        print(f"  - Single frequency issues: {len(single_freq_issues)}")
        print(f"  - Multiple frequency issues: {len(multiple_freq_issues)}")
        
        if len(single_freq_issues) > 0:
            print("✓ Adaptive threshold working - including single frequency issues")
        else:
            print("⚠ Adaptive threshold may not be working as expected")
        
        # Test 3: Validation guidelines generation
        print("\n3. Testing validation guidelines generation...")
        guidelines = generate_validation_guidelines(common_issues)
        print(f"✓ Generated guidelines: {len(guidelines)} characters")
        
        if 'Specific Improvements' in guidelines:
            print("✓ Specific improvements section included")
        else:
            print("⚠ Specific improvements section missing")
        
        # Test 4: Regeneration tracking
        print("\n4. Testing regeneration tracking...")
        initial_count = state.script_regeneration_count
        state.track_script_regeneration("validation_feedback")
        final_count = state.script_regeneration_count
        
        if final_count > initial_count:
            print(f"✓ Regeneration tracking working: {initial_count} → {final_count}")
        else:
            print("⚠ Regeneration tracking not working")
        
        # Test 5: Effectiveness metrics
        print("\n5. Testing feedback effectiveness metrics...")
        metrics = state.get_feedback_effectiveness_metrics()
        
        required_metrics = ['total_feedback_entries', 'regeneration_count', 'regeneration_rate', 
                          'improvement_trend', 'common_issue_categories']
        
        missing_metrics = [metric for metric in required_metrics if metric not in metrics]
        
        if not missing_metrics:
            print("✓ All required metrics present")
            print(f"  - Total feedback entries: {metrics['total_feedback_entries']}")
            print(f"  - Regeneration count: {metrics['regeneration_count']}")
            print(f"  - Regeneration rate: {metrics['regeneration_rate']}")
            print(f"  - Improvement trend: {metrics['improvement_trend']}")
        else:
            print(f"⚠ Missing metrics: {missing_metrics}")
        
        # Test 6: Enhanced prompt generation
        print("\n6. Testing enhanced prompt generation...")
        test_case = {
            'Test Case ID': 'TC001', 
            'Test Case Name': 'Login Test',
            'Steps': [{'Step No': '1', 'Action': 'Navigate to login page'}]
        }
        
        enhanced_prompt = generate_enhanced_test_script_prompt(
            test_case=test_case,
            step_matches={},
            test_data={},
            website_url='https://example.com',
            state=state,
            include_validation_feedback=True
        )
        
        print(f"✓ Enhanced prompt generated: {len(enhanced_prompt)} characters")
        
        # Check for feedback integration
        feedback_indicators = [
            'Recent Feedback Summary',
            'Specific Improvements',
            'validation issues'
        ]
        
        found_indicators = [indicator for indicator in feedback_indicators if indicator in enhanced_prompt]
        
        if found_indicators:
            print(f"✓ Feedback integration working - found: {found_indicators}")
        else:
            print("⚠ Feedback integration may not be working")
        
        print("\n" + "=" * 60)
        print("FEEDBACK LOOP SYSTEM TEST COMPLETED")
        print("=" * 60)
        print("✓ All core functionality tested successfully")
        print("✓ Adaptive frequency threshold implemented")
        print("✓ Enhanced feedback tracking and metrics")
        print("✓ Improved prompt generation with feedback")
        print("✓ Regeneration tracking functional")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_feedback_loop_system()
    sys.exit(0 if success else 1)
