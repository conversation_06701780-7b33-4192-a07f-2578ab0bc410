# Stage Refactoring Example: Stage 1 → Stage 2 Completion

## Overview

This document shows the before/after comparison of how stage completion is handled in the refactored GretahAI ScriptWeaver.

## Before: Boolean Flag Approach

### Old Stage Detection Logic (app.py)
```python
# Complex boolean logic for stage detection
def _get_current_stage_number(state):
    # Stage 1: File upload
    if not (hasattr(state, 'test_cases') and state.test_cases):
        return 1
    
    # Stage 2: Website configuration  
    if not (hasattr(state, 'website_url') and state.website_url and state.website_url != "https://example.com"):
        return 2
        
    # Stage 3: Test case conversion
    if not (hasattr(state, 'selected_test_case') and state.selected_test_case and
            hasattr(state, 'conversion_done') and state.conversion_done):
        return 3
    
    # ... more complex conditions
```

### Old Stage Completion (stage1.py)
```python
# Multiple flag setting
if test_cases_parsed:
    state.test_cases = test_cases
    state.file_uploaded = True
    state.test_cases_loaded = True
    state.ready_for_stage2 = True
    # Manual flag management prone to errors
```

## After: Centralized Stage Management

### New Stage Detection Logic (app.py)
```python
# Simple stage-based routing
current_stage = state.current_stage
logger.info(f"Routing to {current_stage.get_display_name()}")

if current_stage == StateStage.STAGE1_UPLOAD:
    stage1_upload_excel(state)
elif current_stage == StateStage.STAGE2_WEBSITE:
    stage1_upload_excel(state)  # Always show Stage 1
    stage2_enter_website(state)
# ... clean, simple routing
```

### New Stage Completion (stage1.py)
```python
# Single method call with validation and logging
if test_cases_parsed:
    state.test_cases = test_cases
    state.advance_to(StateStage.STAGE2_WEBSITE, f"Successfully loaded {len(test_cases)} test cases")
    # Automatic validation, logging, and flag cleanup
```

## Key Benefits

### 1. **Single Source of Truth**
- `state.current_stage` is the authoritative stage indicator
- No more distributed boolean flag interrogation
- Eliminates phantom stage jumps

### 2. **Validated Transitions**
- `advance_to()` method validates legal transitions
- Prevents invalid stage jumps
- Comprehensive logging for debugging

### 3. **Automatic Cleanup**
- Obsolete flags are automatically cleared
- No manual flag management required
- Prevents stale state issues

### 4. **Simplified Routing**
- Clean if/elif chain based on current_stage
- Easy to understand and maintain
- No complex boolean logic

## Example: Stage 1 → Stage 2 Transition

### StateStage Enum
```python
class StateStage(Enum):
    STAGE1_UPLOAD = "stage1_upload"      # CSV Upload
    STAGE2_WEBSITE = "stage2_website"    # Website Configuration
    STAGE3_CONVERT = "stage3_convert"    # Test Case Analysis and Conversion
    # ... etc
```

### Stage Completion Call
```python
# In stage1.py when file upload completes
state.advance_to(StateStage.STAGE2_WEBSITE, "File upload completed successfully")
```

### What happens internally:
1. **Validation**: Checks if Stage 1 → Stage 2 is a legal transition
2. **Logging**: Records the transition with reason
3. **State Update**: Sets `state.current_stage = StateStage.STAGE2_WEBSITE`
4. **Cleanup**: Clears any obsolete flags from previous stages
5. **Return**: Returns True if successful, False if invalid

### Routing Logic
```python
# In app.py main routing
if current_stage == StateStage.STAGE2_WEBSITE:
    stage1_upload_excel(state)  # Always show Stage 1
    stage2_enter_website(state) # Show Stage 2
```

## Migration Pattern

For each stage completion:

1. **Replace** multiple flag assignments with single `advance_to()` call
2. **Update** imports from `StageEnum` to `StateStage`  
3. **Use** new enum members (e.g., `STAGE2_WEBSITE` instead of `STAGE_2`)
4. **Remove** manual flag cleanup code (handled automatically)

This refactoring eliminates the fragile boolean flag system and provides a robust, centralized stage management approach.
