--- original_script.py
+++ regenerated_script.py
@@ -4,23 +4,45 @@
 from selenium.webdriver.support.ui import WebDriverWait
 from selenium.webdriver.support import expected_conditions as EC
 
-def test_login_functionality(browser):
+def test_login_functionality(browser, test_data):
     """Test login functionality for user authentication."""
     
-    # Navigate to login page
-    browser.get("https://example.com/login")
-    
-    # Find username field using XPath
-    username_field = browser.find_element(By.XPATH, "//input[@type='text']")
-    username_field.send_keys("testuser")
-    
-    # Find password field
-    password_field = browser.find_element(By.XPATH, "//input[@type='password']")
-    password_field.send_keys("password123")
-    
-    # Click login button
-    login_button = browser.find_element(By.XPATH, "//button[contains(text(), 'Login')]")
-    login_button.click()
-    
-    # Check if login was successful
-    assert "dashboard" in browser.current_url
+    try:
+        # Navigate to login page
+        browser.get("https://example.com/login")
+        
+        # Wait for page to load and find username field using CSS selector
+        wait = WebDriverWait(browser, 10)
+        username_field = wait.until(
+            EC.visibility_of_element_located((By.CSS_SELECTOR, "input[name='username']"))
+        )
+        username_field.send_keys(test_data.get("username", "testuser"))
+        
+        # Wait for and find password field using CSS selector
+        password_field = wait.until(
+            EC.visibility_of_element_located((By.CSS_SELECTOR, "input[name='password']"))
+        )
+        password_field.send_keys(test_data.get("password", "password123"))
+        
+        # Wait for login button to be clickable and click it
+        login_button = wait.until(
+            EC.element_to_be_clickable((By.CSS_SELECTOR, "button[data-testid='login-btn']"))
+        )
+        login_button.click()
+        
+        # Wait for navigation to complete and verify successful login
+        wait.until(EC.url_contains("dashboard"))
+        
+        # More specific assertion with proper wait
+        assert "dashboard" in browser.current_url, f"Expected dashboard URL, got: {browser.current_url}"
+        
+        # Verify dashboard elements are visible
+        dashboard_header = wait.until(
+            EC.visibility_of_element_located((By.CSS_SELECTOR, "h1[data-testid='dashboard-title']"))
+        )
+        assert dashboard_header.is_displayed(), "Dashboard header should be visible after login"
+        
+    except Exception as e:
+        # Capture screenshot on failure for debugging
+        browser.save_screenshot(f"login_test_failure_{browser.session_id}.png")
+        raise e