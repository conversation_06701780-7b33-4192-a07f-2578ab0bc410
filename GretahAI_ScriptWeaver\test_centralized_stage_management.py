#!/usr/bin/env python3
"""
Test script for the centralized stage management system in GretahAI ScriptWeaver.

This script validates that the new centralized stage management system correctly:
1. Prevents phantom stage jumps caused by stale boolean flags
2. Provides authoritative stage determination through StageEnum
3. Handles stage transitions with proper validation and cleanup
4. Maintains backward compatibility with existing code

Author: GretahAI ScriptWeaver
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import the StateManager and StageEnum
from state_manager import StateManager, StageEnum

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_centralized_stage_management")


def test_stage_enum_functionality():
    """Test the StageEnum functionality."""
    logger.info("=" * 60)
    logger.info("TESTING STAGE ENUM FUNCTIONALITY")
    logger.info("=" * 60)

    # Test stage number conversion
    assert StageEnum.STAGE_1.get_stage_number() == 1
    assert StageEnum.STAGE_8.get_stage_number() == 8
    logger.info("✅ Stage number conversion works correctly")

    # Test display names
    assert StageEnum.STAGE_1.get_display_name() == "CSV Upload"
    assert StageEnum.STAGE_8.get_display_name() == "Script Consolidation and Optimization"
    logger.info("✅ Display names work correctly")

    # Test from_number conversion
    assert StageEnum.from_number(1) == StageEnum.STAGE_1
    assert StageEnum.from_number(8) == StageEnum.STAGE_8
    logger.info("✅ from_number conversion works correctly")

    # Test invalid stage number
    try:
        StageEnum.from_number(9)
        assert False, "Should have raised ValueError"
    except ValueError:
        logger.info("✅ Invalid stage number properly raises ValueError")


def test_state_manager_initialization():
    """Test StateManager initialization with centralized stage management."""
    logger.info("=" * 60)
    logger.info("TESTING STATE MANAGER INITIALIZATION")
    logger.info("=" * 60)

    # Create a new StateManager instance
    state = StateManager()

    # Verify current_stage is initialized to Stage 1
    assert hasattr(state, 'current_stage'), "StateManager should have current_stage attribute"
    assert state.current_stage == StageEnum.STAGE_1, f"Expected Stage 1, got {state.current_stage}"
    logger.info("✅ StateManager initializes with current_stage = STAGE_1")

    # Test backward compatibility property
    assert state.current_app_stage == 1, f"Expected 1, got {state.current_app_stage}"
    logger.info("✅ Backward compatibility property current_app_stage works correctly")


def test_stage_transition_validation():
    """Test stage transition validation logic."""
    logger.info("=" * 60)
    logger.info("TESTING STAGE TRANSITION VALIDATION")
    logger.info("=" * 60)

    state = StateManager()

    # Test valid forward transitions
    assert state.advance_to_stage(StageEnum.STAGE_2, "Test forward transition")
    assert state.current_stage == StageEnum.STAGE_2
    logger.info("✅ Valid forward transition (Stage 1 -> Stage 2) works")

    # Test invalid backward transition (Stage 2 -> Stage 1 not allowed)
    assert not state.advance_to_stage(StageEnum.STAGE_1, "Invalid backward transition")
    assert state.current_stage == StageEnum.STAGE_2  # Should remain unchanged
    logger.info("✅ Invalid backward transition (Stage 2 -> Stage 1) properly rejected")

    # Test valid legal backward transition (simulate Stage 7 -> Stage 4)
    state.current_stage = StageEnum.STAGE_7
    assert state.advance_to_stage(StageEnum.STAGE_4, "Script execution completed")
    assert state.current_stage == StageEnum.STAGE_4
    logger.info("✅ Valid legal backward transition (Stage 7 -> Stage 4) works")

    # Test Stage 8 -> Stage 3 transition
    state.current_stage = StageEnum.STAGE_8
    assert state.advance_to_stage(StageEnum.STAGE_3, "Optimization completed")
    assert state.current_stage == StageEnum.STAGE_3
    logger.info("✅ Valid legal backward transition (Stage 8 -> Stage 3) works")

    # Test complete reset (Stage 6 -> Stage 1 should be allowed)
    state.current_stage = StageEnum.STAGE_6
    assert state.advance_to_stage(StageEnum.STAGE_1, "Complete application reset")
    assert state.current_stage == StageEnum.STAGE_1
    logger.info("✅ Complete reset (Stage 6 -> Stage 1) works")


def test_flag_cleanup_system():
    """Test the flag cleanup system that prevents phantom stage jumps."""
    logger.info("=" * 60)
    logger.info("TESTING FLAG CLEANUP SYSTEM")
    logger.info("=" * 60)

    state = StateManager()

    # Simulate a scenario that would cause phantom stage jumps in the old system
    # Set flags that would make the old system think we're at Stage 6
    state.uploaded_excel = "test_file.xlsx"
    state.website_url = "https://test.com"
    state.conversion_done = True
    state.step_table_json = {"test": "data"}
    state.selected_test_case = {"Test Case ID": "TC001"}
    state.selected_step = {"step": "data"}
    state.step_matches = {"element": "locator"}
    state.test_data = {"input": "value"}
    state.generated_script_path = "test_script.py"

    # In the old system, this would cause stage detection to jump to Stage 7
    # But with centralized management, we control the stage explicitly
    assert state.current_stage == StageEnum.STAGE_1  # Should still be Stage 1
    logger.info("✅ Stale flags don't cause phantom stage jumps")

    # Now advance to Stage 6 and then use complete reset to Stage 1
    state.advance_to_stage(StageEnum.STAGE_6, "Test flag cleanup")
    state.advance_to_stage(StageEnum.STAGE_1, "Test cleanup transition - complete reset")

    # Verify that flags ahead of Stage 1 were cleaned up
    assert not state.step_ready_for_script, "step_ready_for_script should be cleared"
    assert not state.step_matches, "step_matches should be cleared"
    assert not state.test_data, "test_data should be cleared"
    assert not state.generated_script_path, "generated_script_path should be cleared"
    logger.info("✅ Flag cleanup system properly clears stale flags")


def test_update_stage_based_on_completion():
    """Test the automatic stage detection based on completion criteria."""
    logger.info("=" * 60)
    logger.info("TESTING AUTOMATIC STAGE DETECTION")
    logger.info("=" * 60)

    state = StateManager()

    # Start with no completion
    state.update_stage_based_on_completion()
    assert state.current_stage == StageEnum.STAGE_1
    logger.info("✅ No completion -> Stage 1")

    # Add file upload
    state.uploaded_excel = "test_file.xlsx"
    state.update_stage_based_on_completion()
    assert state.current_stage == StageEnum.STAGE_2
    logger.info("✅ File uploaded -> Stage 2")

    # Add website URL
    state.website_url = "https://test.com"
    state.update_stage_based_on_completion()
    assert state.current_stage == StageEnum.STAGE_3
    logger.info("✅ Website configured -> Stage 3")

    # Add test case conversion
    state.selected_test_case = {"Test Case ID": "TC001"}
    state.conversion_done = True
    state.step_table_json = {"test": "data"}
    state.update_stage_based_on_completion()
    assert state.current_stage == StageEnum.STAGE_4
    logger.info("✅ Test case converted -> Stage 4")


def test_reset_methods_integration():
    """Test that reset methods properly integrate with centralized stage management."""
    logger.info("=" * 60)
    logger.info("TESTING RESET METHODS INTEGRATION")
    logger.info("=" * 60)

    state = StateManager()

    # Advance to Stage 6 and set up state
    state.advance_to_stage(StageEnum.STAGE_6, "Test setup")
    state.selected_test_case = {"Test Case ID": "TC001"}
    state.selected_step = {"step": "data"}
    state.step_matches = {"element": "locator"}

    # Test reset_test_case_state
    state.reset_test_case_state(confirm=True, reason="Test reset integration")

    # Verify stage was reset to Stage 3
    assert state.current_stage == StageEnum.STAGE_3
    logger.info("✅ reset_test_case_state properly resets stage to Stage 3")

    # Verify test case state was cleared
    assert not state.selected_test_case
    assert not state.selected_step
    assert not state.step_matches
    logger.info("✅ reset_test_case_state properly clears test case state")


def run_all_tests():
    """Run all tests for the centralized stage management system."""
    logger.info("🚀 Starting centralized stage management tests...")

    try:
        test_stage_enum_functionality()
        test_state_manager_initialization()
        test_stage_transition_validation()
        test_flag_cleanup_system()
        test_update_stage_based_on_completion()
        test_reset_methods_integration()

        logger.info("=" * 60)
        logger.info("🎉 ALL TESTS PASSED!")
        logger.info("=" * 60)
        logger.info("✅ Centralized stage management system is working correctly")
        logger.info("✅ Phantom stage jumps have been eliminated")
        logger.info("✅ Stage transitions are properly validated")
        logger.info("✅ Flag cleanup prevents stale state issues")
        logger.info("✅ Backward compatibility is maintained")

        return True

    except Exception as e:
        logger.error("=" * 60)
        logger.error("❌ TEST FAILED!")
        logger.error("=" * 60)
        logger.error(f"Error: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
