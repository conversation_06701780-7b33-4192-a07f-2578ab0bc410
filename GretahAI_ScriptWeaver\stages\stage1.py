"""
Stage 1: Upload Excel File

This module handles the Excel file upload and parsing functionality.
Maintains the StateManager pattern and follows the established architectural patterns.
"""

import os
import logging
import tempfile
import streamlit as st
import pandas as pd
from pathlib import Path

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage1")

# Import helper functions from other modules
from core.excel_parser import parse_excel

@st.cache_data
def parse_excel_cached(file_content):
    """
    Cached version of parse_excel function to avoid redundant processing.

    Args:
        file_content (bytes): Raw content of the Excel file

    Returns:
        list: List of test cases
    """
    logger.info("Using cached parse_excel function")

    # Create a temporary file to pass to parse_excel
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
        temp_file.write(file_content)
        temp_file_path = temp_file.name

    try:
        # Parse the Excel file using the existing function
        test_cases = parse_excel(temp_file_path)
        # Clean up the temporary file
        os.unlink(temp_file_path)
        return test_cases
    except Exception as e:
        # Clean up the temporary file even if parsing fails
        os.unlink(temp_file_path)
        logger.error(f"Error in cached parse_excel: {e}")
        raise e

def stage1_upload_excel(state):
    """Phase 1: Upload Excel File."""
    st.markdown("<h2 class='stage-header'>Phase 1: Upload Test Case Excel</h2>", unsafe_allow_html=True)

    # Help text in an expander to reduce visual clutter
    with st.expander("About Excel Format", expanded=False):
        st.markdown("""
        Upload an Excel file with the following columns:
        - **Test Case ID**: Unique identifier
        - **Test Case Objective**: Description of what is being tested
        - **Step No**: Step number
        - **Test Steps**: Action to perform
        - **Expected Result**: Expected outcome
        """)

    # Simplified file uploader with clearer label
    uploaded_file = st.file_uploader("Select Excel file (.xlsx)", type=["xlsx"], key="excel_uploader")

    if uploaded_file is not None:
        try:
            # Get the file content
            file_content = uploaded_file.getvalue()

            # Check if this is the same file we've already processed
            if hasattr(state, 'last_file_content_hash') and state.last_file_content_hash == hash(file_content):
                logger.info("File content unchanged - skipping reprocessing")
            else:
                logger.info("New or changed file detected - processing")
                # Update the content hash in state
                state.last_file_content_hash = hash(file_content)

                # Save the uploaded file to a temporary location
                temp_dir = Path("temp_uploads")
                temp_dir.mkdir(exist_ok=True)

                # Use a consistent filename based on the uploaded file name instead of timestamp
                safe_filename = ''.join(c if c.isalnum() else '_' for c in uploaded_file.name)
                temp_file_path = temp_dir / f"test_cases_{safe_filename}"

                with open(temp_file_path, "wb") as f:
                    f.write(file_content)

                # Update state with file information
                state.uploaded_excel = str(temp_file_path)
                state.uploaded_file = str(temp_file_path)  # Backward compatibility

                # Process file in a collapsible section
                with st.expander("Processing Results", expanded=True):
                    # Verify the file was saved correctly
                    if os.path.exists(temp_file_path) and os.path.getsize(temp_file_path) > 0:
                        state.uploaded_excel = str(temp_file_path)
                        st.success(f"✅ File uploaded: {uploaded_file.name}")

                        # Parse the excel file using the cached function
                        if parse_excel:
                            try:
                                state.test_cases = parse_excel_cached(file_content)
                                if not state.test_cases:
                                    st.warning("⚠️ No test cases found. Check file format.")
                                else:
                                    st.success(f"✅ Parsed {len(state.test_cases)} test cases")

                                    # Advance to Stage 2 using centralized stage management
                                    from state_manager import StateStage
                                    state.advance_to(StateStage.STAGE2_WEBSITE, f"Successfully loaded {len(state.test_cases)} test cases")
                            except Exception as e:
                                st.error(f"❌ Error parsing file: {e}")
                                state.test_cases = None # Ensure it's reset on error
                        else:
                            st.warning("⚠️ Excel parsing function not available")
                    else:
                        st.error("❌ Failed to save file")

            # Always display a preview of the Excel file (using the cached file if available)
            if hasattr(state, 'uploaded_excel') and os.path.exists(state.uploaded_excel):
                try:
                    df = pd.read_excel(state.uploaded_excel) # Read from the saved temp file
                    with st.expander("Preview Test Cases", expanded=True):
                        st.dataframe(df)
                except Exception as e:
                    st.error(f"❌ Error reading file: {e}")
        except Exception as e:
            st.error(f"❌ Error processing file: {e}")
