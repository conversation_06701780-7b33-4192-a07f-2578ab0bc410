#!/usr/bin/env python3
"""
Test script for the new automated code quality validation feature in Stage 6.

This script tests the validation functionality without requiring the full Streamlit app.
"""

import sys
import os
import json
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_validation_prompt_generation():
    """Test the validation prompt generation function."""
    try:
        from core.prompt_builder import generate_script_validation_prompt
        
        # Sample test data
        script_content = '''
import pytest
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def test_login_step_1(browser, test_data):
    """Test login functionality - Step 1"""
    try:
        # Navigate to login page
        browser.get("https://example.com/login")
        
        # Wait for page to load
        Web<PERSON>riverWait(browser, 10).until(
            EC.presence_of_element_located((By.ID, "username"))
        )
        
        # Enter username
        username_field = browser.find_element(By.ID, "username")
        username_field.clear()
        username_field.send_keys(test_data["username"])
        
        # Enter password
        password_field = browser.find_element(By.ID, "password")
        password_field.clear()
        password_field.send_keys(test_data["password"])
        
        # Click login button
        login_button = browser.find_element(By.ID, "login-btn")
        login_button.click()
        
        # Wait for dashboard
        WebDriverWait(browser, 10).until(
            EC.url_contains("/dashboard")
        )
        
        # Assert successful login
        assert "/dashboard" in browser.current_url
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise
'''
        
        test_case = {
            "Test Case ID": "TC001",
            "Test Case Objective": "Verify user can login successfully"
        }
        
        step_table_entry = {
            "step_no": "1",
            "action": "Login with valid credentials",
            "expected": "User is redirected to dashboard"
        }
        
        test_data = {
            "username": "<EMAIL>",
            "password": "TestPassword123"
        }
        
        # Generate validation prompt
        prompt = generate_script_validation_prompt(
            script_content=script_content,
            test_case=test_case,
            step_table_entry=step_table_entry,
            test_data=test_data
        )
        
        logger.info("✅ Validation prompt generated successfully")
        logger.info(f"Prompt length: {len(prompt)} characters")
        
        # Check that prompt contains expected sections
        assert "## Role: You are a senior QA automation code reviewer." in prompt
        assert "## Context" in prompt
        assert "## Script to Validate" in prompt
        assert "## Instructions" in prompt
        assert "## Response Format" in prompt
        assert "TC001" in prompt
        assert "Login with valid credentials" in prompt
        
        logger.info("✅ Validation prompt structure verified")
        return True
        
    except Exception as e:
        logger.error(f"❌ Validation prompt generation test failed: {e}")
        return False

def test_validation_function():
    """Test the script validation function."""
    try:
        from core.ai import validate_generated_script
        
        # Sample test data (same as above)
        script_content = '''
import pytest
from selenium.webdriver.common.by import By

def test_simple(browser):
    browser.get("https://example.com")
    assert "Example" in browser.title
'''
        
        test_case = {
            "Test Case ID": "TC001",
            "Test Case Objective": "Simple test case"
        }
        
        step_table_entry = {
            "step_no": "1",
            "action": "Navigate to homepage",
            "expected": "Page title contains 'Example'"
        }
        
        # Note: This test will fail without a valid API key, but we can test the structure
        try:
            validation_results = validate_generated_script(
                script_content=script_content,
                test_case=test_case,
                step_table_entry=step_table_entry,
                api_key="dummy_key_for_testing"  # This will fail, but we can test error handling
            )
            
            # Check that the function returns the expected structure
            required_keys = ['quality_score', 'syntax_valid', 'issues_found', 'recommendations', 'confidence_rating', 'ready_for_execution']
            for key in required_keys:
                assert key in validation_results, f"Missing key: {key}"
            
            logger.info("✅ Validation function structure verified")
            logger.info(f"Validation results: {json.dumps(validation_results, indent=2)}")
            return True
            
        except Exception as e:
            # Expected to fail with dummy API key, but should still return proper error structure
            logger.info(f"Expected API error (testing with dummy key): {e}")
            return True
            
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Validation function test failed: {e}")
        return False

def test_state_manager_validation_fields():
    """Test that StateManager has the new validation fields."""
    try:
        from state_manager import StateManager
        
        # Create a StateManager instance
        state = StateManager()
        
        # Check that validation fields exist
        assert hasattr(state, 'script_validation_done'), "Missing script_validation_done field"
        assert hasattr(state, 'script_validation_passed'), "Missing script_validation_passed field"
        assert hasattr(state, 'script_validation_results'), "Missing script_validation_results field"
        
        # Check default values
        assert state.script_validation_done == False
        assert state.script_validation_passed == False
        assert state.script_validation_results == {}
        
        logger.info("✅ StateManager validation fields verified")
        return True
        
    except Exception as e:
        logger.error(f"❌ StateManager validation fields test failed: {e}")
        return False

def main():
    """Run all validation tests."""
    logger.info("🧪 Starting validation feature tests...")
    
    tests = [
        ("Validation Prompt Generation", test_validation_prompt_generation),
        ("Validation Function", test_validation_function),
        ("StateManager Validation Fields", test_state_manager_validation_fields)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} PASSED")
                passed += 1
            else:
                logger.error(f"❌ {test_name} FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} FAILED with exception: {e}")
    
    logger.info(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All validation feature tests passed!")
        return True
    else:
        logger.error("💥 Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
