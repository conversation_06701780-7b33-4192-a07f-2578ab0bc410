#!/usr/bin/env python3
"""
Test script to verify the stage progression fix.

This script tests that the stage progression from Stage 1 to Stage 2 works correctly
and doesn't get stuck in a loop.
"""

import sys
import os
sys.path.append('.')

from state_manager import StateManager, StateStage
from stages.stage1 import parse_excel_cached
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('test_stage_progression')

def test_stage_progression_fix():
    """Test that stage progression works correctly."""
    
    print("=== Testing Stage Progression Fix ===")
    
    # Create a mock streamlit session state
    class MockST:
        def __init__(self):
            self.session_state = {}

    mock_st = MockST()

    # Initialize state manager
    state_manager = StateManager()
    state_manager.init_in_session(mock_st)
    state = StateManager.get(mock_st)

    print(f"Initial state: {state.current_stage.get_display_name()}")
    
    # Test 1: Verify initial stage is Stage 1
    assert state.current_stage == StateStage.STAGE1_UPLOAD, f"Expected Stage 1, got {state.current_stage}"
    print("✅ Test 1 passed: Initial stage is Stage 1")
    
    # Test 2: Simulate file upload and parsing
    # Use an existing test file
    test_file = "temp_uploads/test_cases_Latest_testcases_TP_1_ALL_20250426_081659_xlsx"
    if os.path.exists(test_file):
        print(f"Using test file: {test_file}")
        
        # Read the file content
        with open(test_file, 'rb') as f:
            file_content = f.read()
        
        # Set up state as if file was uploaded
        state.uploaded_excel = test_file
        state.last_file_content_hash = hash(file_content)
        
        # Parse the file
        try:
            test_cases = parse_excel_cached(file_content)
            state.test_cases = test_cases
            print(f"Parsed {len(test_cases)} test cases")
            
            # Test 3: Advance to Stage 2 (only if in Stage 1)
            if state.current_stage == StateStage.STAGE1_UPLOAD:
                result = state.advance_to(StateStage.STAGE2_WEBSITE, f"Successfully loaded {len(test_cases)} test cases")
                assert result == True, "Stage advancement should succeed"
                print("✅ Test 3 passed: Successfully advanced to Stage 2")
            else:
                print("⚠️ Test 3 skipped: Not in Stage 1")
            
            # Test 4: Verify we're now in Stage 2
            assert state.current_stage == StateStage.STAGE2_WEBSITE, f"Expected Stage 2, got {state.current_stage}"
            print("✅ Test 4 passed: Now in Stage 2")
            
            # Test 5: Simulate calling stage1 function again (as happens in app routing)
            # This should NOT trigger another stage advancement
            print("Testing stage1 function when already in Stage 2...")
            
            # Check that we don't advance again
            current_stage_before = state.current_stage
            
            # The fix should prevent advancement when not in Stage 1
            if state.current_stage == StateStage.STAGE1_UPLOAD:
                state.advance_to(StateStage.STAGE2_WEBSITE, f"Successfully loaded {len(test_cases)} test cases")
            else:
                print("✅ Test 5 passed: Stage advancement correctly skipped when not in Stage 1")
            
            # Verify stage didn't change
            assert state.current_stage == current_stage_before, "Stage should not have changed"
            print("✅ Test 6 passed: Stage remained stable")
            
        except Exception as e:
            print(f"❌ Error during testing: {e}")
            return False
    else:
        print(f"❌ Test file not found: {test_file}")
        return False
    
    print("\n=== All Tests Passed! ===")
    print("The stage progression fix is working correctly.")
    return True

if __name__ == "__main__":
    success = test_stage_progression_fix()
    sys.exit(0 if success else 1)
