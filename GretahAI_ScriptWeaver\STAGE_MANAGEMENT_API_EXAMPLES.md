# Stage Management API Examples

## Overview

This document provides comprehensive code examples demonstrating proper usage of the centralized stage management API in GretahAI ScriptWeaver. These examples show how to implement stage transitions, handle errors, and maintain proper workflow control.

## Core API Reference

### StageEnum Usage

```python
from state_manager import StageEnum

# Available stages
StageEnum.STAGE_1  # CSV Upload
StageEnum.STAGE_2  # Website Configuration
StageEnum.STAGE_3  # Test Case Analysis and Conversion
StageEnum.STAGE_4  # UI Element Detection and Step Selection
StageEnum.STAGE_5  # Manual Data Entry
StageEnum.STAGE_6  # Test Script Generation
StageEnum.STAGE_7  # Test Script Execution
StageEnum.STAGE_8  # Script Consolidation and Optimization

# Utility methods
stage_num = StageEnum.STAGE_3.get_stage_number()  # Returns 3
display_name = StageEnum.STAGE_3.get_display_name()  # Returns "Test Case Analysis and Conversion"
stage_from_num = StageEnum.from_number(3)  # Returns StageEnum.STAGE_3
```

### Basic Stage Transitions

```python
# Forward transition (always allowed)
success = state.advance_to_stage(StageEnum.STAGE_2, "User uploaded test cases")
if success:
    st.success("Advanced to Stage 2")
    st.rerun()
else:
    st.error("Failed to advance to Stage 2")

# Backward transition (only if legal)
success = state.advance_to_stage(StageEnum.STAGE_4, "Script execution completed, returning to step selection")
if success:
    st.success("Returned to Stage 4 for next step")
    st.rerun()
else:
    st.error("Illegal transition - cannot go back to Stage 4 from current stage")

# Complete reset (always allowed)
success = state.advance_to_stage(StageEnum.STAGE_1, "User requested complete reset")
if success:
    st.success("Application reset to Stage 1")
    st.rerun()
```

## Stage-Specific Examples

### Stage 1: File Upload Completion

```python
def handle_file_upload_completion(state, test_cases):
    """Handle successful file upload and advance to Stage 2"""
    try:
        # Store the parsed test cases
        state.test_cases = test_cases
        
        # Advance to Stage 2 using centralized management
        success = state.advance_to_stage(
            StageEnum.STAGE_2, 
            f"Successfully loaded {len(test_cases)} test cases"
        )
        
        if success:
            st.success(f"✅ Loaded {len(test_cases)} test cases. Proceeding to website configuration.")
            st.rerun()
        else:
            st.error("Failed to advance to website configuration stage")
            
    except Exception as e:
        st.error(f"Error processing file upload: {e}")
        # Don't advance stage on error
```

### Stage 2: Website Configuration

```python
def handle_website_configuration(state, website_url):
    """Handle website URL configuration and advance to Stage 3"""
    try:
        # Validate the URL
        if not website_url or website_url == "https://example.com":
            st.warning("Please enter a valid website URL")
            return
        
        # Store the website URL
        state.website_url = website_url
        
        # Advance to Stage 3
        success = state.advance_to_stage(
            StageEnum.STAGE_3,
            f"Website URL configured: {website_url}"
        )
        
        if success:
            st.success("✅ Website configured. Proceeding to test case analysis.")
            st.rerun()
        else:
            st.error("Failed to advance to test case analysis stage")
            
    except Exception as e:
        st.error(f"Error configuring website: {e}")
```

### Stage 3: Test Case Conversion

```python
def handle_test_case_conversion_completion(state, step_table_json):
    """Handle successful test case conversion and advance to Stage 4"""
    try:
        # Store conversion results
        state.step_table_json = step_table_json
        state.conversion_done = True
        
        # Advance to Stage 4
        success = state.advance_to_stage(
            StageEnum.STAGE_4,
            f"Test case converted to {len(step_table_json)} steps"
        )
        
        if success:
            st.success("✅ Test case converted. Proceeding to UI element detection.")
            st.rerun()
        else:
            st.error("Failed to advance to UI element detection stage")
            
    except Exception as e:
        st.error(f"Error in test case conversion: {e}")
        # Reset conversion state on error
        state.conversion_done = False
        state.step_table_json = None
```

### Stage 6: Script Generation

```python
def handle_script_generation_completion(state, script_path):
    """Handle successful script generation and advance to Stage 7"""
    try:
        # Store the generated script path
        state.generated_script_path = script_path
        
        # Advance to Stage 7
        success = state.advance_to_stage(
            StageEnum.STAGE_7,
            f"Script generated: {script_path}"
        )
        
        if success:
            st.success("✅ Test script generated. Proceeding to script execution.")
            st.rerun()
        else:
            st.error("Failed to advance to script execution stage")
            
    except Exception as e:
        st.error(f"Error in script generation: {e}")
        # Clear script path on error
        state.generated_script_path = None
```

### Stage 7: Script Execution and Step Advancement

```python
def handle_script_execution_completion(state):
    """Handle script execution completion and determine next action"""
    try:
        # Check if there are more steps to process
        next_step_index = state.current_step_index + 1
        
        if next_step_index < state.total_steps:
            # More steps available - return to Stage 4 for next step
            success = state.advance_to_stage(
                StageEnum.STAGE_4,
                f"Script execution completed, advancing to step {next_step_index + 1}"
            )
            
            if success:
                # Set flag for Stage 4 to handle step advancement
                st.session_state['coming_from_stage7'] = True
                st.success(f"✅ Step completed. Advancing to next step.")
                st.rerun()
            else:
                st.error("Failed to advance to next step")
        else:
            # All steps completed - set flag for Stage 8 transition
            state.all_steps_done = True
            st.success("✅ All test steps completed!")
            st.rerun()
            
    except Exception as e:
        st.error(f"Error handling script execution completion: {e}")
```

### Stage 7 to Stage 8: Optimization Choice

```python
def handle_optimization_choice(state, user_choice):
    """Handle user choice to proceed to optimization or start new test case"""
    try:
        if user_choice == "optimize":
            # User chose to optimize - advance to Stage 8
            success = state.advance_to_stage(
                StageEnum.STAGE_8,
                "User chose to proceed to script optimization"
            )
            
            if success:
                st.session_state['transitioning_to_stage8'] = True
                st.success("✅ Proceeding to script optimization.")
                st.rerun()
            else:
                st.error("Failed to advance to optimization stage")
                
        elif user_choice == "new_test_case":
            # User chose new test case - reset and go to Stage 3
            success = state.reset_test_case_state(
                confirm=True,
                reason="User chose to start new test case"
            )
            
            if success:
                st.success("✅ Starting new test case selection.")
                st.rerun()
            else:
                st.error("Failed to reset for new test case")
                
    except Exception as e:
        st.error(f"Error handling optimization choice: {e}")
```

### Stage 8: Optimization Completion

```python
def handle_optimization_completion(state, optimized_script_path):
    """Handle optimization completion and return to Stage 3"""
    try:
        # Store optimization results
        state.optimized_script_path = optimized_script_path
        state.optimization_complete = True
        
        # Reset test case state and return to Stage 3
        success = state.reset_test_case_state(
            confirm=True,
            reason="Script optimization complete, returning to test case selection"
        )
        
        if success:
            st.session_state['transitioning_from_stage8'] = True
            st.success("✅ Script optimization complete. Returning to test case selection.")
            st.rerun()
        else:
            st.error("Failed to return to test case selection")
            
    except Exception as e:
        st.error(f"Error handling optimization completion: {e}")
```

## Error Handling Examples

### Handling Illegal Transitions

```python
def safe_stage_transition(state, target_stage, reason):
    """Safely attempt a stage transition with error handling"""
    try:
        success = state.advance_to_stage(target_stage, reason)
        
        if success:
            st.success(f"✅ Advanced to {target_stage.get_display_name()}")
            st.rerun()
        else:
            # Transition was rejected - show user-friendly message
            current_stage = state.current_stage.get_display_name()
            target_stage_name = target_stage.get_display_name()
            
            st.error(f"❌ Cannot transition from {current_stage} to {target_stage_name}")
            st.info("This transition is not allowed in the current workflow state.")
            
    except Exception as e:
        st.error(f"❌ Error during stage transition: {e}")
        # Log the error for debugging
        import logging
        logger = logging.getLogger("ScriptWeaver.stage_transition")
        logger.error(f"Stage transition error: {e}", exc_info=True)
```

### Handling State Corruption

```python
def validate_and_recover_state(state):
    """Validate state consistency and recover if needed"""
    try:
        # Check if current_stage exists
        if not hasattr(state, 'current_stage'):
            st.warning("⚠️ Stage information missing. Recovering...")
            state.update_stage_based_on_completion()
            st.success("✅ Stage information recovered")
            st.rerun()
            
        # Validate stage consistency
        current_stage_num = state.current_stage.get_stage_number()
        
        # Check for impossible state combinations
        if current_stage_num >= 3 and not state.test_cases:
            st.warning("⚠️ Inconsistent state detected. Resetting to Stage 1...")
            state.advance_to_stage(StageEnum.STAGE_1, "State recovery - missing test cases")
            st.rerun()
            
        if current_stage_num >= 7 and not state.generated_script_path:
            st.warning("⚠️ Inconsistent state detected. Resetting to Stage 6...")
            state.advance_to_stage(StageEnum.STAGE_6, "State recovery - missing script")
            st.rerun()
            
    except Exception as e:
        st.error(f"❌ Error during state validation: {e}")
        # Ultimate fallback - reset to Stage 1
        state.advance_to_stage(StageEnum.STAGE_1, "Emergency state recovery")
        st.rerun()
```

## State Reset Examples

### Complete Application Reset

```python
def handle_complete_reset(state):
    """Handle complete application reset"""
    try:
        # Confirm with user
        if st.button("🔄 Reset Application", type="secondary"):
            confirm = st.checkbox("I understand this will clear all progress")
            
            if confirm:
                success = state.advance_to_stage(
                    StageEnum.STAGE_1,
                    "User requested complete application reset"
                )
                
                if success:
                    st.success("✅ Application reset successfully")
                    st.rerun()
                else:
                    st.error("❌ Failed to reset application")
            else:
                st.warning("Please confirm the reset by checking the box above")
                
    except Exception as e:
        st.error(f"Error during application reset: {e}")
```

### Test Case Level Reset

```python
def handle_test_case_reset(state):
    """Handle test case level reset"""
    try:
        if st.button("🔄 Select Different Test Case", type="secondary"):
            success = state.reset_test_case_state(
                confirm=True,
                reason="User requested different test case"
            )
            
            if success:
                st.success("✅ Ready to select a different test case")
                st.rerun()
            else:
                st.error("❌ Failed to reset test case state")
                
    except Exception as e:
        st.error(f"Error during test case reset: {e}")
```

### Step Level Reset

```python
def handle_step_reset(state):
    """Handle step level reset"""
    try:
        if st.button("🔄 Restart Current Step", type="secondary"):
            success = state.reset_step_state(
                confirm=True,
                reason="User requested step restart"
            )
            
            if success:
                st.success("✅ Step state reset. You can reconfigure this step.")
                st.rerun()
            else:
                st.error("❌ Failed to reset step state")
                
    except Exception as e:
        st.error(f"Error during step reset: {e}")
```

## Best Practices

### 1. Always Check Return Values

```python
# ✅ Good - Check return value
success = state.advance_to_stage(StageEnum.STAGE_2, "File uploaded")
if success:
    st.rerun()
else:
    st.error("Transition failed")

# ❌ Bad - Ignore return value
state.advance_to_stage(StageEnum.STAGE_2, "File uploaded")
st.rerun()  # May rerun with failed transition
```

### 2. Provide Meaningful Reasons

```python
# ✅ Good - Descriptive reason
state.advance_to_stage(StageEnum.STAGE_7, f"Script generated for step {step_no}")

# ❌ Bad - Generic reason
state.advance_to_stage(StageEnum.STAGE_7, "next stage")
```

### 3. Handle Errors Gracefully

```python
# ✅ Good - Comprehensive error handling
try:
    success = state.advance_to_stage(target_stage, reason)
    if success:
        st.success("Transition successful")
        st.rerun()
    else:
        st.error("Transition not allowed")
except Exception as e:
    st.error(f"Error: {e}")
    logger.error(f"Stage transition error: {e}", exc_info=True)
```

### 4. Use Appropriate Reset Methods

```python
# ✅ Good - Use specific reset methods
state.reset_test_case_state(confirm=True, reason="New test case")
state.reset_step_state(confirm=True, reason="Step restart")

# ❌ Bad - Manual flag clearing
state.selected_test_case = None
state.conversion_done = False
```

## Conclusion

The centralized stage management API provides a robust, predictable way to control workflow transitions in GretahAI ScriptWeaver. By following these examples and best practices, developers can ensure reliable stage management that eliminates phantom stage jumps and provides a smooth user experience.
