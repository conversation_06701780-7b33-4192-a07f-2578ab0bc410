#!/usr/bin/env python3
"""
Test script to verify that the regeneration tracking fix is working correctly.

This script simulates the regeneration workflow and verifies:
1. Regeneration count increments properly when buttons are clicked
2. Session state is properly synchronized with StateManager
3. Feedback loop integration works correctly
4. State persistence across regeneration attempts
"""

import sys
import os
import logging
sys.path.append('.')

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_regeneration_tracking_fix():
    """Test the regeneration tracking fix."""
    
    print("=" * 80)
    print("TESTING REGENERATION TRACKING FIX")
    print("=" * 80)
    
    try:
        # Import required modules
        from state_manager import StateManager
        
        print("✓ Successfully imported StateManager")
        
        # Create test state manager
        state = StateManager()
        
        # Add some validation feedback to simulate a real scenario
        validation_feedback = {
            'timestamp': '2025-01-01T10:00:00',
            'test_case_id': 'TC001',
            'step_no': '1',
            'quality_score': 65,
            'issues_found': [
                {'category': 'locators', 'severity': 'medium', 'description': 'Using generic XPath selectors'},
                {'category': 'waits', 'severity': 'high', 'description': 'Missing WebDriverWait conditions'}
            ],
            'recommendations': [
                'Use CSS selectors instead of XPath',
                'Add explicit wait conditions before interactions'
            ],
            'confidence_rating': 'medium',
            'ready_for_execution': True
        }
        
        state.add_validation_feedback(validation_feedback, 'TC001', '1')
        print("✓ Added validation feedback to state")
        
        # Test 1: Initial state
        print("\n1. Testing initial state...")
        initial_count = state.script_regeneration_count
        print(f"   Initial regeneration count: {initial_count}")
        assert initial_count == 0, f"Expected initial count to be 0, got {initial_count}"
        print("   ✓ Initial state correct")
        
        # Test 2: First regeneration tracking
        print("\n2. Testing first regeneration tracking...")
        state.track_script_regeneration("main_button_regeneration")
        first_count = state.script_regeneration_count
        print(f"   After first regeneration: {first_count}")
        assert first_count == 1, f"Expected count to be 1, got {first_count}"
        print("   ✓ First regeneration tracking working")
        
        # Test 3: Second regeneration tracking
        print("\n3. Testing second regeneration tracking...")
        state.track_script_regeneration("validation_feedback")
        second_count = state.script_regeneration_count
        print(f"   After second regeneration: {second_count}")
        assert second_count == 2, f"Expected count to be 2, got {second_count}"
        print("   ✓ Second regeneration tracking working")
        
        # Test 4: Feedback effectiveness metrics
        print("\n4. Testing feedback effectiveness metrics...")
        metrics = state.get_feedback_effectiveness_metrics()
        
        expected_metrics = ['total_feedback_entries', 'regeneration_count', 'regeneration_rate']
        for metric in expected_metrics:
            assert metric in metrics, f"Missing metric: {metric}"
            print(f"   {metric}: {metrics[metric]}")
        
        # Verify regeneration count in metrics
        assert metrics['regeneration_count'] == 2, f"Expected regeneration_count to be 2, got {metrics['regeneration_count']}"
        assert metrics['regeneration_rate'] == 1.0, f"Expected regeneration_rate to be 1.0, got {metrics['regeneration_rate']}"
        print("   ✓ Feedback effectiveness metrics correct")
        
        # Test 5: Common issues retrieval
        print("\n5. Testing common issues retrieval...")
        common_issues = state.get_common_validation_issues(limit=8)
        print(f"   Retrieved {len(common_issues)} common issues")
        assert len(common_issues) > 0, "Should have common issues from feedback"
        print("   ✓ Common issues retrieval working")
        
        # Test 6: Session state simulation
        print("\n6. Testing session state synchronization...")
        
        # Simulate session state
        mock_session_state = {
            'regeneration_attempt': 0,
            'regeneration_in_progress': False,
            'feedback_guidelines_count': 0
        }
        
        # Simulate the synchronization logic from stage6.py
        state_regen_count = getattr(state, 'script_regeneration_count', 0)
        session_regen_count = mock_session_state.get('regeneration_attempt', 0)
        regeneration_attempt = max(state_regen_count, session_regen_count)
        
        print(f"   State regeneration count: {state_regen_count}")
        print(f"   Session regeneration count: {session_regen_count}")
        print(f"   Calculated regeneration attempt: {regeneration_attempt}")
        
        # Update session state if state has a higher count
        if state_regen_count > session_regen_count:
            mock_session_state['regeneration_attempt'] = state_regen_count
            print(f"   Updated session regeneration_attempt to {state_regen_count}")
        
        assert regeneration_attempt == 2, f"Expected regeneration_attempt to be 2, got {regeneration_attempt}"
        assert mock_session_state['regeneration_attempt'] == 2, f"Expected session state to be updated to 2, got {mock_session_state['regeneration_attempt']}"
        print("   ✓ Session state synchronization working")
        
        # Test 7: Button text generation
        print("\n7. Testing button text generation...")
        button_text = f"🔄 Regenerate Script (Attempt #{regeneration_attempt + 1})" if regeneration_attempt > 0 else "Generate Test Script"
        expected_text = "🔄 Regenerate Script (Attempt #3)"
        print(f"   Generated button text: {button_text}")
        assert button_text == expected_text, f"Expected '{expected_text}', got '{button_text}'"
        print("   ✓ Button text generation working")
        
        # Test 8: Spinner text generation
        print("\n8. Testing spinner text generation...")
        current_attempt = max(regeneration_attempt, state.script_regeneration_count)
        spinner_text = f"🔄 Regenerating script with feedback loop (Attempt #{current_attempt})..." if current_attempt > 0 else "Generating test script..."
        expected_spinner = "🔄 Regenerating script with feedback loop (Attempt #2)..."
        print(f"   Generated spinner text: {spinner_text}")
        assert spinner_text == expected_spinner, f"Expected '{expected_spinner}', got '{spinner_text}'"
        print("   ✓ Spinner text generation working")
        
        print("\n" + "=" * 80)
        print("REGENERATION TRACKING FIX VERIFICATION COMPLETED")
        print("=" * 80)
        
        print("\n📊 SUMMARY OF VERIFIED FIXES:")
        print("✓ Regeneration count increments properly")
        print("✓ State persistence across regeneration attempts")
        print("✓ Session state synchronization working")
        print("✓ Button text shows correct attempt number")
        print("✓ Spinner text shows correct attempt number")
        print("✓ Feedback effectiveness metrics reflect regenerations")
        print("✓ Common issues retrieval working for feedback loop")
        
        print("\n🎯 EXPECTED BEHAVIOR:")
        print("- First click: Regeneration count 0 → 1")
        print("- Second click: Regeneration count 1 → 2")
        print("- Button text updates to show next attempt number")
        print("- Feedback loop uses validation guidelines")
        print("- Session state stays synchronized with StateManager")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the regeneration tracking fix test."""
    
    print("Starting regeneration tracking fix verification...")
    print("This test verifies that the regeneration count properly increments.")
    
    success = test_regeneration_tracking_fix()
    
    if success:
        print("\n🎉 ALL TESTS PASSED!")
        print("The regeneration tracking fix is working correctly.")
        print("Users should now see proper regeneration count increments.")
    else:
        print("\n❌ TESTS FAILED!")
        print("Please review the error messages and fix any remaining issues.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
