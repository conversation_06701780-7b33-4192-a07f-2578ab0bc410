#!/usr/bin/env python3
"""
Test script for regeneration attempt limiting functionality in GretahAI ScriptWeaver Stage 6.

This script validates that the regeneration limiting system works correctly to prevent
infinite AI regeneration loops and provides clear feedback to users.
"""

import sys
import os
import logging
from dataclasses import dataclass
from typing import Dict, Any

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_configuration():
    """Test that the MAX_REGENERATIONS configuration is properly set."""
    logger.info("Testing configuration setup...")

    try:
        from core.config import MAX_REGENERATIONS
        assert MAX_REGENERATIONS == 3, f"Expected MAX_REGENERATIONS=3, got {MAX_REGENERATIONS}"
        logger.info(f"✅ Configuration test passed: MAX_REGENERATIONS = {MAX_REGENERATIONS}")
        return True
    except ImportError as e:
        logger.error(f"❌ Configuration test failed: Could not import MAX_REGENERATIONS - {e}")
        return False
    except AssertionError as e:
        logger.error(f"❌ Configuration test failed: {e}")
        return False

def test_state_manager_fields():
    """Test that StateManager has the required regeneration tracking fields."""
    logger.info("Testing StateManager regeneration fields...")

    try:
        from state_manager import StateManager

        # Create a test instance
        state = StateManager()

        # Check that regen_attempts field exists and is initialized to 0
        assert hasattr(state, 'regen_attempts'), "StateManager missing regen_attempts field"
        assert state.regen_attempts == 0, f"Expected regen_attempts=0, got {state.regen_attempts}"

        # Test that we can increment the counter
        state.regen_attempts += 1
        assert state.regen_attempts == 1, f"Expected regen_attempts=1 after increment, got {state.regen_attempts}"

        logger.info("✅ StateManager fields test passed")
        return True
    except Exception as e:
        logger.error(f"❌ StateManager fields test failed: {e}")
        return False

def test_reset_step_state():
    """Test that reset_step_state properly resets the regeneration counter."""
    logger.info("Testing reset_step_state regeneration counter reset...")

    try:
        from state_manager import StateManager

        # Create a test instance
        state = StateManager()

        # Set some regeneration attempts
        state.regen_attempts = 2
        assert state.regen_attempts == 2, "Failed to set initial regen_attempts"

        # Call reset_step_state with confirmation
        result = state.reset_step_state(confirm=True, reason="Test reset")

        # Check that regeneration counter was reset
        assert result == True, "reset_step_state should return True"
        assert state.regen_attempts == 0, f"Expected regen_attempts=0 after reset, got {state.regen_attempts}"

        logger.info("✅ reset_step_state test passed")
        return True
    except Exception as e:
        logger.error(f"❌ reset_step_state test failed: {e}")
        return False

def test_regeneration_limiting_logic():
    """Test the regeneration limiting logic."""
    logger.info("Testing regeneration limiting logic...")

    try:
        from core.config import MAX_REGENERATIONS
        from state_manager import StateManager

        # Create a test instance
        state = StateManager()

        # Test that regeneration is allowed when under limit
        state.regen_attempts = 1
        regeneration_allowed = state.regen_attempts < MAX_REGENERATIONS
        assert regeneration_allowed == True, f"Expected regeneration allowed at attempt 1, got {regeneration_allowed}"

        # Test that regeneration is blocked when at limit
        state.regen_attempts = MAX_REGENERATIONS
        regeneration_blocked = state.regen_attempts >= MAX_REGENERATIONS
        assert regeneration_blocked == True, f"Expected regeneration blocked at attempt {MAX_REGENERATIONS}, got {regeneration_blocked}"

        # Test that regeneration is blocked when over limit
        state.regen_attempts = MAX_REGENERATIONS + 1
        regeneration_blocked = state.regen_attempts >= MAX_REGENERATIONS
        assert regeneration_blocked == True, f"Expected regeneration blocked at attempt {MAX_REGENERATIONS + 1}, got {regeneration_blocked}"

        logger.info("✅ Regeneration limiting logic test passed")
        return True
    except Exception as e:
        logger.error(f"❌ Regeneration limiting logic test failed: {e}")
        return False

def test_error_handling():
    """Test error handling when configuration is not available."""
    logger.info("Testing error handling for missing configuration...")

    try:
        # Simulate missing configuration by temporarily removing it from sys.modules
        import sys
        config_module = sys.modules.get('core.config')
        if config_module:
            del sys.modules['core.config']

        # Test that the fallback logic works
        try:
            from core.config import MAX_REGENERATIONS
            fallback_value = 3
        except ImportError:
            fallback_value = 3  # This is what the code should do

        assert fallback_value == 3, f"Expected fallback value 3, got {fallback_value}"

        # Restore the module
        if config_module:
            sys.modules['core.config'] = config_module

        logger.info("✅ Error handling test passed")
        return True
    except Exception as e:
        logger.error(f"❌ Error handling test failed: {e}")
        return False

def run_all_tests():
    """Run all regeneration limiting tests."""
    logger.info("=" * 60)
    logger.info("REGENERATION LIMITING FUNCTIONALITY TESTS")
    logger.info("=" * 60)

    tests = [
        ("Configuration Setup", test_configuration),
        ("StateManager Fields", test_state_manager_fields),
        ("Reset Step State", test_reset_step_state),
        ("Regeneration Limiting Logic", test_regeneration_limiting_logic),
        ("Error Handling", test_error_handling)
    ]

    passed = 0
    failed = 0

    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"❌ {test_name} failed with exception: {e}")
            failed += 1

    logger.info("\n" + "=" * 60)
    logger.info("TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    logger.info(f"✅ Passed: {passed}")
    logger.info(f"❌ Failed: {failed}")
    logger.info(f"📊 Total: {passed + failed}")

    if failed == 0:
        logger.info("🎉 All tests passed! Regeneration limiting functionality is working correctly.")
        return True
    else:
        logger.error(f"⚠️ {failed} test(s) failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
