# GretahA<PERSON> ScriptWeaver - Complete Workflow Architecture Flowchart

## Overview
This document provides a comprehensive flowchart of the GretahAI ScriptWeaver application workflow, including all 8 stages, state management, feedback loops, validation systems, and decision points.

## Workflow Architecture Diagram

```mermaid
flowchart TD
    %% Entry Point
    START([Application Start]) --> INIT[Initialize StateManager<br/>Load Session State]

    %% Stage Detection Logic
    INIT --> STAGE_DETECT{Detect Current Stage<br/>Based on State}

    %% Stage 1: CSV Upload
    STAGE_DETECT --> S1[Stage 1: CSV Upload<br/>Upload Excel File]
    S1 --> S1_CHECK{Excel File<br/>Uploaded?}
    S1_CHECK -->|No| S1
    S1_CHECK -->|Yes| S1_COMPLETE[✓ Stage 1 Complete<br/>state.uploaded_file set]

    %% Stage 2: Website Configuration
    S1_COMPLETE --> S2[Stage 2: Website Configuration<br/>Enter Website URL]
    S2 --> S2_CHECK{Website URL<br/>Configured?}
    S2_CHECK -->|No| S2
    S2_CHECK -->|Yes| S2_COMPLETE[✓ Stage 2 Complete<br/>state.website_url set]

    %% Stage 3: Test Case Analysis
    S2_COMPLETE --> S3[Stage 3: Test Case Analysis<br/>Convert Test Cases via AI]
    S3 --> S3_AI[Google AI API Call<br/>generate_llm_response]
    S3_AI --> S3_CHECK{Test Case<br/>Converted?}
    S3_CHECK -->|No| S3_ERROR[Show Error<br/>Retry Option]
    S3_ERROR --> S3
    S3_CHECK -->|Yes| S3_COMPLETE[✓ Stage 3 Complete<br/>state.conversion_done = True<br/>state.step_table_json set]

    %% Stage 4: Element Matching & Step Selection
    S3_COMPLETE --> S4[Stage 4: UI Element Detection<br/>& Test Case Step Selection]
    S4 --> S4_STEP_SELECT[Step Selection UI<br/>Manual Step Navigation]
    S4_STEP_SELECT --> S4_ELEMENT[Element Matching<br/>AI or Manual]
    S4_ELEMENT --> S4_CHECK{Step Ready<br/>for Script?}
    S4_CHECK -->|No| S4_STEP_SELECT
    S4_CHECK -->|Yes| S4_COMPLETE[✓ Stage 4 Complete<br/>state.step_ready_for_script = True<br/>state.step_matches set]

    %% Stage 5: Manual Data Entry
    S4_COMPLETE --> S5[Stage 5: Manual Data Entry<br/>Configure Test Data]
    S5 --> S5_INPUT[Manual Input Fields<br/>Per Test Step]
    S5_INPUT --> S5_CHECK{Test Data<br/>Configured?}
    S5_CHECK -->|Skip| S5_SKIP[state.test_data_skipped = True]
    S5_CHECK -->|Configure| S5_SAVE[state.manual_test_data set]
    S5_SKIP --> S5_COMPLETE[✓ Stage 5 Complete]
    S5_SAVE --> S5_COMPLETE

    %% Stage 6: Script Generation
    S5_COMPLETE --> S6[Stage 6: Test Script Generation<br/>Two-Phase Generation]
    S6 --> S6_PHASE1[Phase 1: Generate Isolated Script<br/>Google AI API Call]
    S6_PHASE1 --> S6_PHASE2[Phase 2: Merge with Previous<br/>Google AI API Call]
    S6_PHASE2 --> S6_VALIDATE[Automated Code Quality<br/>Validation]

    %% Validation System Branch
    S6_VALIDATE --> S6_TRANSPARENT{Use Transparent<br/>Scoring?}
    S6_TRANSPARENT -->|Yes| S6_TRANS_SCORE[TransparentScorer.score_script<br/>Rule-based Validation]
    S6_TRANSPARENT -->|No| S6_AI_VALIDATE[AI-Powered Validation<br/>validate_generated_script]

    S6_TRANS_SCORE --> S6_SCORE_RESULT[Quality Score: 0-100<br/>Detailed Breakdown]
    S6_AI_VALIDATE --> S6_SCORE_RESULT

    S6_SCORE_RESULT --> S6_FEEDBACK{Quality Score<br/>≥ 80?}
    S6_FEEDBACK -->|No| S6_REGEN[Show Recommendations<br/>Regenerate Option]
    S6_REGEN --> S6_FEEDBACK_LOOP[Feed Recommendations<br/>into Prompt Builder]
    S6_FEEDBACK_LOOP --> S6_PHASE1
    S6_FEEDBACK -->|Yes| S6_COMPLETE[✓ Stage 6 Complete<br/>state.generated_script_path set<br/>state.script_validation_passed = True]

    %% Stage 7: Script Execution
    S6_COMPLETE --> S7[Stage 7: Test Script Execution<br/>Run PyTest Scripts]
    S7 --> S7_EXEC[Execute Test Script<br/>Capture Results]
    S7_EXEC --> S7_RESULT{Execution<br/>Result?}

    %% Error Handling Branch
    S7_RESULT -->|Error| S7_ERROR[Display Error Details<br/>Pause Workflow]
    S7_ERROR --> S7_ERROR_UI[Error Acknowledgment UI<br/>Continue/Retry Options]
    S7_ERROR_UI --> S7_ACK{User Action?}
    S7_ACK -->|Acknowledge| S7_CLEAR_ERROR[Clear Error State<br/>Set coming_from_stage7 flag]
    S7_ACK -->|Retry| S7_EXEC
    S7_CLEAR_ERROR --> S4_RETURN[Return to Stage 4<br/>Step Selection]
    S4_RETURN --> S4

    %% Success Branch
    S7_RESULT -->|Success| S7_SUCCESS[Test Passed<br/>Capture Screenshots]
    S7_SUCCESS --> S7_ADVANCE{More Steps<br/>Available?}
    S7_ADVANCE -->|Yes| S7_NEXT[advance_to_next_step<br/>Set coming_from_stage7 flag]
    S7_NEXT --> S4_RETURN
    S7_ADVANCE -->|No| S7_ALL_DONE[state.all_steps_done = True<br/>All Steps Completed]

    %% Stage 7 to Stage 8/3 Decision
    S7_ALL_DONE --> S7_DECISION[Manual Decision UI<br/>Two Options Presented]
    S7_DECISION --> S7_CHOICE{User Choice?}
    S7_CHOICE -->|Optimize| S7_TO_S8[Set transitioning_to_stage8 flag<br/>Proceed to Stage 8]
    S7_CHOICE -->|Skip| S7_TO_S3[Reset Test Case State<br/>Return to Stage 3]
    S7_TO_S3 --> S3

    %% Stage 8: Script Optimization
    S7_TO_S8 --> S8[Stage 8: Script Consolidation<br/>& Optimization]
    S8 --> S8_COMBINE[Create Combined Script<br/>from All Steps]
    S8_COMBINE --> S8_OPTIMIZE[Google AI API Call<br/>optimize_script_with_ai]
    S8_OPTIMIZE --> S8_VALIDATE[AI-Powered Validation<br/>of Optimized Script]

    %% Stage 8 Validation (Similar to Stage 6)
    S8_VALIDATE --> S8_TRANS{Use Transparent<br/>Scoring?}
    S8_TRANS -->|Yes| S8_TRANS_SCORE[TransparentScorer.score_script<br/>Rule-based Validation]
    S8_TRANS -->|No| S8_AI_VAL[AI-Powered Validation]

    S8_TRANS_SCORE --> S8_RESULT[Optimization Results<br/>Before/After Comparison]
    S8_AI_VAL --> S8_RESULT

    S8_RESULT --> S8_TEST[Test Optimized Script<br/>Reuse Stage 7 Logic]
    S8_TEST --> S8_DOWNLOAD[Download Optimized Script<br/>Timestamped Filename]
    S8_DOWNLOAD --> S8_COMPLETE[✓ Stage 8 Complete<br/>state.optimization_complete = True]

    %% Stage 8 to Stage 3 Transition
    S8_COMPLETE --> S8_TO_S3[Set transitioning_from_stage8 flag<br/>Return to Stage 3]
    S8_TO_S3 --> S3

    %% State Management Components
    subgraph STATE_MGR [StateManager Components]
        SM_UPDATE[update_step_progress<br/>Centralized State Updates]
        SM_RESET[reset_step_state<br/>reset_test_case_state]
        SM_VALIDATE[State Validation<br/>Transition Guards]
        SM_LOG[Comprehensive Logging<br/>State Change Tracking]
    end

    %% Session State Flags
    subgraph FLAGS [Session State Flags]
        FLAG_S7[coming_from_stage7<br/>Stage 7 → Stage 4 transition]
        FLAG_S8_TO[transitioning_to_stage8<br/>Stage 7 → Stage 8 transition]
        FLAG_S8_FROM[transitioning_from_stage8<br/>Stage 8 → Stage 3 transition]
        FLAG_FORCE[force_refresh_after_advance<br/>UI refresh control]
    end

    %% AI Integration Points
    subgraph AI_SYSTEM [AI Integration System]
        AI_CENTRAL[generate_llm_response<br/>Centralized AI Gateway]
        AI_LOG[AI Logger<br/>Complete Function Traces]
        AI_PROMPT[prompt_builder.py<br/>Enhanced Prompt Generation]
        AI_VALIDATE[Validation Feedback Loop<br/>Recommendations → Prompts]
    end

    %% Validation Systems
    subgraph VALIDATION [Validation Systems]
        TRANS_SCORE[Transparent Scoring<br/>Rule-based, Auditable]
        AI_SCORE[AI-Powered Scoring<br/>Natural Language Analysis]
        FEEDBACK[Feedback Loop System<br/>Quality Improvement]
        CRITERIA[10-Point Validation Criteria<br/>Syntax, WebDriver, Best Practices]
    end

    %% Styling
    classDef stageBox fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef decisionBox fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef completeBox fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef errorBox fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef systemBox fill:#f3e5f5,stroke:#4a148c,stroke-width:2px

    class S1,S2,S3,S4,S5,S6,S7,S8 stageBox
    class S1_CHECK,S2_CHECK,S3_CHECK,S4_CHECK,S5_CHECK,S6_FEEDBACK,S7_RESULT,S7_ADVANCE,S7_CHOICE,S8_TRANS decisionBox
    class S1_COMPLETE,S2_COMPLETE,S3_COMPLETE,S4_COMPLETE,S5_COMPLETE,S6_COMPLETE,S7_ALL_DONE,S8_COMPLETE completeBox
    class S3_ERROR,S7_ERROR,S7_ERROR_UI errorBox
    class STATE_MGR,FLAGS,AI_SYSTEM,VALIDATION systemBox
```

## Key Architecture Components

### 1. Stage Flow Progression
- **Sequential Stages 1-8**: Each stage has specific completion criteria
- **Conditional Execution**: Stages only run when prerequisites are met
- **State-Driven Navigation**: Current stage determined by StateManager state

### 2. StateManager Patterns
- **Centralized State**: All application state managed through StateManager dataclass
- **State Validation**: Transition guards prevent invalid state changes
- **Comprehensive Logging**: All state changes logged with before/after values
- **Helper Methods**: `update_step_progress()`, `reset_step_state()`, `reset_test_case_state()`

### 3. Feedback Loop Mechanisms
- **Stage 7 → Stage 4**: After script execution, return to step selection
- **Stage 8 → Stage 3**: After optimization, return to test case selection
- **Validation Feedback**: Quality recommendations fed back into prompt generation
- **Error Handling**: Pause workflow on errors, require user acknowledgment

### 4. Validation Systems
- **Transparent Scoring**: Rule-based, auditable scoring (0-100 points)
- **AI-Powered Validation**: Natural language analysis of code quality
- **10-Point Criteria**: Syntax, WebDriver usage, locators, wait conditions, etc.
- **Feedback Integration**: Recommendations improve future script generation

### 5. Decision Points & Manual Controls
- **Manual Progression**: Confirmation buttons required for stage advancement
- **User Choices**: Stage 7 offers optimization vs. skip options
- **Error Acknowledgment**: Users must acknowledge errors before continuing
- **Step Navigation**: Manual step selection in Stage 4

### 6. Data Flow Architecture
- **Test Data**: CSV → Converted JSON → Element Matches → Generated Scripts
- **Script Evolution**: Isolated Script → Merged Script → Optimized Script
- **Validation Results**: Quality Scores → Recommendations → Improved Prompts
- **Session Persistence**: State maintained across browser sessions

This architecture ensures robust workflow management, comprehensive error handling, and continuous quality improvement through AI-powered feedback loops.

## Detailed Implementation Analysis

### Stage Transition Logic

#### Stage Detection Algorithm (app.py lines 443-481)
```python
# Hierarchical stage detection based on state completeness
current_app_stage = 1  # Default to Stage 1

if state.uploaded_file:
    current_app_stage = 2
    if state.website_url and state.website_url != "https://example.com":
        current_app_stage = 3
        if state.selected_test_case and state.conversion_done:
            current_app_stage = 4
            if state.selected_step and state.step_matches:
                current_app_stage = 5
                if state.test_data or state.test_data_skipped:
                    current_app_stage = 6
                    if state.generated_script_path:
                        current_app_stage = 7
                        if state.all_steps_done:
                            current_app_stage = 8
```

#### Conditional Stage Execution (app.py lines 854-982)
- **Stage 8 Priority**: Always runs first if optimization conditions are met
- **Suite Level (1-2)**: Always executed unconditionally
- **Test Case Level (3)**: Runs if suite prerequisites are met
- **Step Level (4-7)**: Runs if test case is selected and converted
- **coming_from_stage7 Flag**: Stops execution at Stage 4 for user interaction

### StateManager Implementation Details

#### Core State Categories (state_manager.py lines 34-122)
1. **Core Test-Run Metadata**: Basic test case and step information
2. **Step-Progress Counters**: Track progress through multi-step test cases
3. **Per-Step Artifacts**: Generated scripts, analysis results, element matches
4. **Browser and Element Detection**: UI elements and browser instances
5. **Flags**: Boolean indicators for application flow control
6. **Usage Tracking**: Metrics for API usage and performance

#### State Update Methods
- `update_step_progress()`: Centralized step advancement with validation
- `reset_step_state()`: Clears step-specific state with confirmation
- `reset_test_case_state()`: Resets entire test case with confirmation
- Comprehensive logging of all state changes with before/after values

### Feedback Loop Implementation

#### Stage 7 → Stage 4 Transition (stage7.py lines 725-742)
```python
# Set flag for return to Stage 4
st.session_state['coming_from_stage7'] = True
logger.info(f"Setting coming_from_stage7 flag to return to Stage 4 with step {next_step_no}")

# Force state update and advance to next step
st.session_state['state'] = state
advance_to_next_step()  # Calls st.rerun() internally
```

#### Stage 8 → Stage 3 Transition (stage8.py lines 838-846)
```python
# Set flag for return to Stage 3
st.session_state['transitioning_from_stage8'] = True
st.session_state['stage_progression_message'] = "✅ Script optimization complete. Returning to Phase 3 to select a new test case."

# Reset test case state and force rerun
state.reset_test_case_state(confirm=True, reason="User chose to return to Phase 3 after optimization")
st.rerun()
```

### Validation System Architecture

#### Transparent Scoring System (transparent_scoring.py)
- **Rule-Based Scoring**: 10 categories with specific point allocations
- **Auditable Results**: Detailed breakdown showing exactly how points are awarded
- **Scientific Rigor**: Deterministic, repeatable scoring methodology
- **Categories**: Syntax (15pts), WebDriver Usage (15pts), Locators (15pts), Wait Conditions (10pts), etc.

#### AI-Powered Validation (ai.py lines 751-908)
- **Centralized Gateway**: All AI calls routed through `generate_llm_response()`
- **Comprehensive Logging**: Complete function call traces with stack information
- **Fallback System**: Transparent scoring as primary, AI as supplementary
- **Quality Thresholds**: Scripts with <80% score trigger regeneration

#### Validation Feedback Loop (prompt_builder.py lines 515-649)
```python
def enhance_prompt_with_validation_feedback(base_prompt: str, validation_history: List[Dict]) -> str:
    """Enhance prompts with validation recommendations for improved quality."""
    common_issues = extract_common_issues(validation_history)
    validation_guidelines = generate_validation_guidelines(common_issues)
    return inject_guidelines_into_prompt(base_prompt, validation_guidelines)
```

### Error Handling & Recovery

#### Script Execution Error Handling (stage7.py lines 863-884)
- **Error Capture**: Full traceback and error details preserved
- **Workflow Pause**: Execution stops, requires user acknowledgment
- **Recovery Options**: Continue workflow or retry execution
- **State Management**: Error state tracked in StateManager

#### Error Acknowledgment Flow
1. **Error Detection**: Script execution fails with exception
2. **Error Display**: Full error details shown to user
3. **User Decision**: Acknowledge error or retry execution
4. **State Cleanup**: Clear error state and set transition flags
5. **Workflow Resume**: Return to Stage 4 for step selection

### Session State Management

#### Critical Session Flags
- `coming_from_stage7`: Controls Stage 7 → Stage 4 transition
- `transitioning_to_stage8`: Controls Stage 7 → Stage 8 transition
- `transitioning_from_stage8`: Controls Stage 8 → Stage 3 transition
- `force_refresh_after_advance`: Ensures UI refresh after step advancement
- `optimization_in_progress`: Tracks Stage 8 optimization status

#### State Persistence Strategy
- **Session State**: Temporary flags for workflow control
- **StateManager**: Persistent application state across sessions
- **Validation**: State consistency checks on every transition
- **Recovery**: Graceful handling of invalid state conditions

### AI Integration Architecture

#### Centralized AI Gateway (ai.py)
```python
def generate_llm_response(prompt, model_name, api_key, context, category, function_name):
    """Centralized gateway for all AI interactions with comprehensive logging."""
    # Log request details
    # Call Google AI API
    # Log response details
    # Return processed response
```

#### AI Logging System
- **Request Logging**: Complete prompt, model, and context information
- **Response Logging**: Full AI response with metadata
- **Performance Tracking**: API call duration and token usage
- **Error Tracking**: Failed requests with retry logic

This comprehensive architecture provides a robust, maintainable, and scalable foundation for the GretahAI ScriptWeaver application, ensuring reliable workflow management and continuous quality improvement.

## Summary of Key Decision Points

### Critical Workflow Decisions

| Decision Point | Location | Conditions | Outcomes |
|---|---|---|---|
| **Stage Detection** | app.py:443-481 | State completeness hierarchy | Determines which stages to execute |
| **Stage 8 Execution** | app.py:767-857 | 3 conditions: transitioning, in-progress, complete | Runs Stage 8 or continues normal flow |
| **coming_from_stage7** | app.py:954-961 | Flag set in session state | Stops at Stage 4 for user interaction |
| **Script Quality** | Stage 6 validation | Quality score ≥ 80% | Proceed to Stage 7 or regenerate |
| **Execution Result** | Stage 7 execution | Success/failure status | Advance to next step or show error |
| **All Steps Done** | Stage 7 completion | No more steps available | Show Stage 8/Stage 3 choice |
| **Optimization Choice** | Stage 7 decision UI | User manual selection | Proceed to Stage 8 or return to Stage 3 |
| **Error Acknowledgment** | Stage 7 error handling | User acknowledgment required | Return to Stage 4 or retry |

### Data Flow Transformation

```mermaid
graph LR
    subgraph "Data Evolution Pipeline"
        CSV[Excel/CSV File<br/>Raw Test Cases] --> JSON[JSON Step Table<br/>AI-Converted Structure]
        JSON --> MATCHES[Element Matches<br/>UI Locator Mapping]
        MATCHES --> DATA[Manual Test Data<br/>User Input Values]
        DATA --> ISOLATED[Isolated Script<br/>Single Step Code]
        ISOLATED --> MERGED[Merged Script<br/>Multi-Step Sequence]
        MERGED --> VALIDATED[Validated Script<br/>Quality Assured]
        VALIDATED --> EXECUTED[Executed Script<br/>Test Results]
        EXECUTED --> COMBINED[Combined Script<br/>All Steps Merged]
        COMBINED --> OPTIMIZED[Optimized Script<br/>Production Ready]
    end

    subgraph "Validation Checkpoints"
        V1[Stage 3: AI Conversion<br/>JSON Structure Validation]
        V2[Stage 6: Script Generation<br/>Quality Score Validation]
        V3[Stage 7: Script Execution<br/>Runtime Validation]
        V4[Stage 8: Script Optimization<br/>Final Quality Validation]
    end

    JSON -.-> V1
    VALIDATED -.-> V2
    EXECUTED -.-> V3
    OPTIMIZED -.-> V4
```

### State Management Lifecycle

```mermaid
stateDiagram-v2
    [*] --> Initialized: StateManager.init_in_session()

    Initialized --> Stage1: Upload file
    Stage1 --> Stage2: state.uploaded_file = file
    Stage2 --> Stage3: state.website_url = url
    Stage3 --> Stage4: state.conversion_done = True

    Stage4 --> StepSelection: Select test step
    StepSelection --> Stage5: state.step_ready_for_script = True
    Stage5 --> Stage6: state.test_data configured
    Stage6 --> Stage7: state.generated_script_path set

    Stage7 --> NextStep: More steps available
    Stage7 --> AllDone: All steps completed

    NextStep --> StepReset: reset_step_state()
    StepReset --> Stage4: coming_from_stage7 flag

    AllDone --> UserChoice: Manual decision
    UserChoice --> Stage8: Optimize choice
    UserChoice --> TestCaseReset: Skip choice

    Stage8 --> TestCaseReset: reset_test_case_state()
    TestCaseReset --> Stage3: New test case

    Stage7 --> ErrorState: Execution failure
    ErrorState --> ErrorAck: User acknowledgment
    ErrorAck --> StepReset: Continue workflow
    ErrorAck --> Stage7: Retry execution
```

## Implementation Best Practices

### 1. State Management Principles
- **Single Source of Truth**: StateManager holds all application state
- **Immutable Updates**: State changes through dedicated methods only
- **Validation Guards**: Prevent invalid state transitions
- **Comprehensive Logging**: Track all state changes with context

### 2. Error Handling Strategy
- **Graceful Degradation**: Application continues despite individual failures
- **User Control**: Users decide how to handle errors (retry/continue)
- **State Recovery**: Clean error state before workflow resumption
- **Detailed Reporting**: Full error context for debugging

### 3. AI Integration Patterns
- **Centralized Gateway**: All AI calls through single function
- **Comprehensive Logging**: Complete request/response tracking
- **Fallback Systems**: Multiple validation approaches
- **Quality Feedback**: Validation results improve future prompts

### 4. UI/UX Design Principles
- **Manual Progression**: User confirmation required for major transitions
- **Clear Feedback**: Visual indicators for all state changes
- **Collapsible Sections**: Reduce visual clutter with expandable content
- **Consistent Patterns**: Standardized UI components across stages

This architecture documentation provides a complete technical reference for understanding, maintaining, and extending the GretahAI ScriptWeaver application workflow system.
