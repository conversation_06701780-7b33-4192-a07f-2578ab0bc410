#!/usr/bin/env python3
"""
Demonstration script showing the difference between original and regenerated scripts
using the improved feedback loop system in GretahAI ScriptWeaver.

This script creates realistic examples of:
1. Original script (without feedback)
2. Validation feedback identifying issues
3. Regenerated script (with feedback loop improvements)
4. Side-by-side comparison with analysis
"""

import difflib
import sys
import os
sys.path.append('.')

def create_original_script():
    """Create an example of an original script with common issues."""
    return '''import pytest
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def test_login_functionality(browser):
    """Test login functionality for user authentication."""
    
    # Navigate to login page
    browser.get("https://example.com/login")
    
    # Find username field using XPath
    username_field = browser.find_element(By.XPATH, "//input[@type='text']")
    username_field.send_keys("testuser")
    
    # Find password field
    password_field = browser.find_element(By.XPATH, "//input[@type='password']")
    password_field.send_keys("password123")
    
    # Click login button
    login_button = browser.find_element(By.XPATH, "//button[contains(text(), 'Login')]")
    login_button.click()
    
    # Check if login was successful
    assert "dashboard" in browser.current_url
'''

def create_validation_feedback():
    """Create realistic validation feedback identifying issues in the original script."""
    return {
        'quality_score': 65,
        'issues_found': [
            {
                'category': 'locators',
                'severity': 'medium',
                'description': 'Using generic XPath selectors instead of more specific CSS selectors'
            },
            {
                'category': 'waits',
                'severity': 'high',
                'description': 'Missing WebDriverWait conditions before element interactions'
            },
            {
                'category': 'assertions',
                'severity': 'medium',
                'description': 'Weak assertion - only checking URL contains text without waiting'
            },
            {
                'category': 'test_data',
                'severity': 'low',
                'description': 'Hardcoded test data instead of using test_data fixture'
            },
            {
                'category': 'error_handling',
                'severity': 'medium',
                'description': 'No try/except block for error handling and screenshots'
            }
        ],
        'recommendations': [
            'Use CSS selectors with specific attributes (id, name, data-testid)',
            'Add explicit WebDriverWait conditions before all element interactions',
            'Use more specific assertions with proper wait conditions',
            'Integrate test_data fixture for dynamic test values',
            'Add try/except block with screenshot capture on failure'
        ],
        'confidence_rating': 'medium',
        'ready_for_execution': True,
        'validation_status': 'needs_improvement'
    }

def create_regenerated_script():
    """Create an example of a regenerated script with feedback loop improvements."""
    return '''import pytest
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def test_login_functionality(browser, test_data):
    """Test login functionality for user authentication."""
    
    try:
        # Navigate to login page
        browser.get("https://example.com/login")
        
        # Wait for page to load and find username field using CSS selector
        wait = WebDriverWait(browser, 10)
        username_field = wait.until(
            EC.visibility_of_element_located((By.CSS_SELECTOR, "input[name='username']"))
        )
        username_field.send_keys(test_data.get("username", "testuser"))
        
        # Wait for and find password field using CSS selector
        password_field = wait.until(
            EC.visibility_of_element_located((By.CSS_SELECTOR, "input[name='password']"))
        )
        password_field.send_keys(test_data.get("password", "password123"))
        
        # Wait for login button to be clickable and click it
        login_button = wait.until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, "button[data-testid='login-btn']"))
        )
        login_button.click()
        
        # Wait for navigation to complete and verify successful login
        wait.until(EC.url_contains("dashboard"))
        
        # More specific assertion with proper wait
        assert "dashboard" in browser.current_url, f"Expected dashboard URL, got: {browser.current_url}"
        
        # Verify dashboard elements are visible
        dashboard_header = wait.until(
            EC.visibility_of_element_located((By.CSS_SELECTOR, "h1[data-testid='dashboard-title']"))
        )
        assert dashboard_header.is_displayed(), "Dashboard header should be visible after login"
        
    except Exception as e:
        # Capture screenshot on failure for debugging
        browser.save_screenshot(f"login_test_failure_{browser.session_id}.png")
        raise e
'''

def generate_detailed_diff(original, regenerated):
    """Generate a detailed side-by-side diff with analysis."""
    
    # Split into lines for comparison
    original_lines = original.splitlines()
    regenerated_lines = regenerated.splitlines()
    
    # Generate HTML diff
    diff = difflib.HtmlDiff(tabsize=4, wrapcolumn=100)
    html_diff = diff.make_file(
        original_lines,
        regenerated_lines,
        fromdesc="Original Script (No Feedback)",
        todesc="Regenerated Script (With Feedback Loop)",
        context=True,
        numlines=3
    )
    
    # Generate unified diff for text analysis
    unified_diff = list(difflib.unified_diff(
        original_lines,
        regenerated_lines,
        fromfile="original_script.py",
        tofile="regenerated_script.py",
        lineterm=""
    ))
    
    return html_diff, unified_diff

def analyze_improvements(original, regenerated, feedback):
    """Analyze specific improvements made based on feedback."""
    
    improvements = {
        'locator_improvements': [],
        'wait_improvements': [],
        'assertion_improvements': [],
        'test_data_improvements': [],
        'error_handling_improvements': []
    }
    
    # Analyze locator improvements
    if 'XPath' in original and 'CSS_SELECTOR' in regenerated:
        improvements['locator_improvements'].append({
            'issue': 'Generic XPath selectors',
            'improvement': 'Replaced with specific CSS selectors using name and data-testid attributes',
            'example': 'By.XPATH, "//input[@type=\'text\']" → By.CSS_SELECTOR, "input[name=\'username\']"'
        })
    
    # Analyze wait improvements
    if 'find_element' in original and 'wait.until' in regenerated:
        improvements['wait_improvements'].append({
            'issue': 'Direct element finding without waits',
            'improvement': 'Added explicit WebDriverWait conditions before all interactions',
            'example': 'browser.find_element() → wait.until(EC.visibility_of_element_located())'
        })
    
    # Analyze assertion improvements
    if 'assert "dashboard" in browser.current_url' in original and 'wait.until(EC.url_contains' in regenerated:
        improvements['assertion_improvements'].append({
            'issue': 'Immediate assertion without waiting',
            'improvement': 'Added wait condition before assertion and more descriptive error message',
            'example': 'Direct URL check → wait.until(EC.url_contains()) with detailed assertion'
        })
    
    # Analyze test data improvements
    if 'test_data' not in original and 'test_data.get(' in regenerated:
        improvements['test_data_improvements'].append({
            'issue': 'Hardcoded test values',
            'improvement': 'Integrated test_data fixture with fallback values',
            'example': '"testuser" → test_data.get("username", "testuser")'
        })
    
    # Analyze error handling improvements
    if 'try:' not in original and 'try:' in regenerated:
        improvements['error_handling_improvements'].append({
            'issue': 'No error handling or debugging support',
            'improvement': 'Added try/except block with screenshot capture on failure',
            'example': 'No error handling → try/except with browser.save_screenshot()'
        })
    
    return improvements

def print_comparison_report(original, regenerated, feedback, improvements):
    """Print a comprehensive comparison report."""
    
    print("=" * 80)
    print("FEEDBACK LOOP SYSTEM: SCRIPT IMPROVEMENT ANALYSIS")
    print("=" * 80)
    
    print("\n📊 VALIDATION FEEDBACK SUMMARY")
    print("-" * 40)
    print(f"Quality Score: {feedback['quality_score']}/100")
    print(f"Issues Found: {len(feedback['issues_found'])}")
    print(f"Recommendations: {len(feedback['recommendations'])}")
    print(f"Validation Status: {feedback['validation_status']}")
    
    print("\n🔍 ISSUES IDENTIFIED")
    print("-" * 40)
    for i, issue in enumerate(feedback['issues_found'], 1):
        severity_icon = "🔴" if issue['severity'] == 'high' else "🟡" if issue['severity'] == 'medium' else "🔵"
        print(f"{i}. {severity_icon} {issue['category'].title()}: {issue['description']}")
    
    print("\n💡 RECOMMENDATIONS PROVIDED")
    print("-" * 40)
    for i, rec in enumerate(feedback['recommendations'], 1):
        print(f"{i}. {rec}")
    
    print("\n✅ IMPROVEMENTS IMPLEMENTED")
    print("-" * 40)
    
    for category, improvements_list in improvements.items():
        if improvements_list:
            category_name = category.replace('_', ' ').title()
            print(f"\n{category_name}:")
            for imp in improvements_list:
                print(f"  • Issue: {imp['issue']}")
                print(f"    Solution: {imp['improvement']}")
                print(f"    Example: {imp['example']}")
    
    print("\n📈 SCRIPT QUALITY METRICS")
    print("-" * 40)
    
    # Calculate metrics
    original_lines = len(original.splitlines())
    regenerated_lines = len(regenerated.splitlines())
    
    original_waits = original.count('WebDriverWait')
    regenerated_waits = regenerated.count('WebDriverWait')
    
    original_css = original.count('CSS_SELECTOR')
    regenerated_css = regenerated.count('CSS_SELECTOR')
    
    original_xpath = original.count('XPATH')
    regenerated_xpath = regenerated.count('XPATH')
    
    original_assertions = original.count('assert')
    regenerated_assertions = regenerated.count('assert')
    
    print(f"Lines of Code: {original_lines} → {regenerated_lines} ({regenerated_lines - original_lines:+d})")
    print(f"WebDriverWait Usage: {original_waits} → {regenerated_waits} ({regenerated_waits - original_waits:+d})")
    print(f"CSS Selectors: {original_css} → {regenerated_css} ({regenerated_css - original_css:+d})")
    print(f"XPath Selectors: {original_xpath} → {regenerated_xpath} ({regenerated_xpath - original_xpath:+d})")
    print(f"Assertions: {original_assertions} → {regenerated_assertions} ({regenerated_assertions - original_assertions:+d})")
    
    # Calculate improvement score
    improvement_score = 0
    if regenerated_waits > original_waits:
        improvement_score += 20
    if regenerated_css > original_css:
        improvement_score += 15
    if regenerated_xpath < original_xpath:
        improvement_score += 15
    if 'try:' in regenerated and 'try:' not in original:
        improvement_score += 20
    if 'test_data' in regenerated and 'test_data' not in original:
        improvement_score += 15
    if regenerated_assertions > original_assertions:
        improvement_score += 15
    
    print(f"\nEstimated Quality Improvement: +{improvement_score} points")
    print(f"Projected New Quality Score: {feedback['quality_score'] + improvement_score}/100")

def main():
    """Run the feedback loop comparison demonstration."""
    
    # Generate example scripts and feedback
    original_script = create_original_script()
    validation_feedback = create_validation_feedback()
    regenerated_script = create_regenerated_script()
    
    # Analyze improvements
    improvements = analyze_improvements(original_script, regenerated_script, validation_feedback)
    
    # Print comprehensive report
    print_comparison_report(original_script, regenerated_script, validation_feedback, improvements)
    
    # Generate diff files for detailed comparison
    html_diff, unified_diff = generate_detailed_diff(original_script, regenerated_script)
    
    print("\n📄 DIFF FILES GENERATED")
    print("-" * 40)
    
    # Save HTML diff
    with open("script_comparison.html", "w") as f:
        f.write(html_diff)
    print("✓ HTML diff saved to: script_comparison.html")
    
    # Save unified diff
    with open("script_comparison.diff", "w") as f:
        f.write('\n'.join(unified_diff))
    print("✓ Unified diff saved to: script_comparison.diff")
    
    print("\n🎯 FEEDBACK LOOP EFFECTIVENESS")
    print("-" * 40)
    print("✓ All validation recommendations were successfully implemented")
    print("✓ Script quality improved from 65/100 to estimated 85+/100")
    print("✓ Enhanced error handling and debugging capabilities")
    print("✓ Better maintainability with CSS selectors and test data integration")
    print("✓ Improved reliability with explicit wait conditions")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
