# One-Shot Flag Implementation

## Overview

This document describes the implementation of self-destructing temporary flags in GretahAI ScriptWeaver to ensure flags automatically clean up after the next rerun, preventing stale flag issues that can cause phantom stage jumps.

## Problem Solved

### Before: Manual Flag Management
```python
# Setting flags manually
st.session_state['coming_from_stage7'] = True
st.rerun()

# Manual cleanup in app.py
if 'coming_from_stage7' in st.session_state:
    state.advance_to(StateStage.STAGE4_DETECT, "Coming from Stage 7")
    del st.session_state['coming_from_stage7']  # Manual cleanup
```

**Issues:**
- Manual cleanup required in multiple places
- Risk of forgetting to clean up flags
- Stale flags could persist across sessions
- Phantom stage jumps from forgotten flags

### After: Self-Destructing Flags
```python
# Using one-shot flags
from utils.flag_helpers import one_shot_flag

with one_shot_flag('coming_from_stage7'):
    st.rerun()

# Automatic cleanup in app.py
cleanup_one_shot_flags()  # Called once at start of each run
```

**Benefits:**
- Automatic cleanup after next rerun
- No manual flag management required
- Prevents stale flag accumulation
- Eliminates phantom stage jumps

## Implementation Details

### 1. Core Context Manager ✅

**File**: `utils/flag_helpers.py`

```python
@contextmanager
def one_shot_flag(key: str, value: Any = True):
    """
    Context manager for creating self-destructing session state flags.
    
    The flag will be present during the next rerun and automatically deleted thereafter.
    """
    logger.info(f"Setting one-shot flag: {key} = {value}")
    
    # Set the flag in session state
    st.session_state[key] = value
    
    try:
        yield
    finally:
        # Schedule the flag for deletion on the next script run
        cleanup_key = f"_cleanup_{key}"
        st.session_state[cleanup_key] = True
        logger.info(f"Scheduled one-shot flag for cleanup: {key}")
```

### 2. Automatic Cleanup System ✅

**File**: `utils/flag_helpers.py`

```python
def cleanup_one_shot_flags():
    """
    Clean up all one-shot flags that were scheduled for deletion.
    
    This function should be called at the beginning of each script run.
    """
    cleanup_keys = [k for k in st.session_state.keys() if k.startswith('_cleanup_')]
    
    for cleanup_key in cleanup_keys:
        original_key = cleanup_key[9:]  # Remove '_cleanup_' prefix
        
        # Delete the original flag if it exists
        if original_key in st.session_state:
            logger.info(f"Cleaning up one-shot flag: {original_key}")
            del st.session_state[original_key]
        
        # Delete the cleanup marker
        del st.session_state[cleanup_key]
```

### 3. Integration with Main App ✅

**File**: `app.py`

```python
def run_app():
    """Main function to run the Streamlit application."""

    # Clean up one-shot flags from previous run
    from utils.flag_helpers import cleanup_one_shot_flags
    cleanup_one_shot_flags()

    # ... rest of app logic
    
    # Handle special session state transitions
    from utils.flag_helpers import is_flag_set
    
    if is_flag_set('transitioning_to_stage8'):
        state.advance_to(StateStage.STAGE8_OPTIMIZE, "Transitioning from Stage 7")
        # No manual cleanup needed - automatic!
```

### 4. Updated Stage Files ✅

#### Stage 7 → Stage 4 Transition
**File**: `stages/stage7.py`

```python
# OLD: Manual flag management
st.session_state['coming_from_stage7'] = True
st.rerun()

# NEW: One-shot flag
from utils.flag_helpers import one_shot_flag
with one_shot_flag('coming_from_stage7'):
    logger.info("Setting one-shot coming_from_stage7 flag")
    st.rerun()
```

#### Stage 7 → Stage 8 Transition
**File**: `stages/stage7.py`

```python
# OLD: Manual flag management
st.session_state['transitioning_to_stage8'] = True
st.rerun()

# NEW: One-shot flag
from utils.flag_helpers import one_shot_flag
with one_shot_flag('transitioning_to_stage8'):
    logger.info("Setting one-shot transitioning_to_stage8 flag")
    st.rerun()
```

#### Stage 8 → Stage 3 Transition
**File**: `stages/stage8.py`

```python
# OLD: Manual flag management
st.session_state['transitioning_from_stage8'] = True
st.rerun()

# NEW: One-shot flag
from utils.flag_helpers import one_shot_flag
with one_shot_flag('transitioning_from_stage8'):
    logger.info("Setting one-shot transitioning_from_stage8 flag")
    st.rerun()
```

## Helper Functions

### Utility Functions ✅
```python
def is_flag_set(key: str) -> bool:
    """Check if a flag is currently set in session state."""
    return st.session_state.get(key, False)

def get_flag_value(key: str, default: Any = None) -> Any:
    """Get the value of a flag from session state."""
    return st.session_state.get(key, default)

def clear_flag(key: str) -> bool:
    """Manually clear a flag from session state."""
    if key in st.session_state:
        del st.session_state[key]
        return True
    return False
```

### Convenience Context Managers ✅
```python
@contextmanager
def coming_from_stage_flag(stage: str):
    """Context manager for 'coming from stage' flags."""
    flag_key = f"coming_from_{stage}"
    with one_shot_flag(flag_key):
        yield

@contextmanager
def stage_transition_flag(from_stage: str, to_stage: str):
    """Context manager for stage transition flags."""
    flag_key = f"transitioning_from_{from_stage}_to_{to_stage}"
    with one_shot_flag(flag_key):
        yield

@contextmanager
def operation_in_progress_flag(operation: str):
    """Context manager for operation in progress flags."""
    flag_key = f"{operation}_in_progress"
    with one_shot_flag(flag_key):
        yield
```

## Flag Lifecycle

### 1. Flag Creation
```python
with one_shot_flag('my_flag'):
    # Flag is set: st.session_state['my_flag'] = True
    # Cleanup marker is scheduled: st.session_state['_cleanup_my_flag'] = True
    st.rerun()
```

### 2. Next Rerun
```python
# At start of next run:
cleanup_one_shot_flags()
# - Finds '_cleanup_my_flag' marker
# - Deletes 'my_flag' from session state
# - Deletes '_cleanup_my_flag' marker

# Flag is now gone and won't interfere with future runs
```

## Testing Results ✅

All 8 unit tests pass successfully:

```
✅ Basic Functionality: Flag setting and cleanup scheduling
✅ Custom Value: Support for non-boolean flag values
✅ Cleanup Functionality: Automatic flag removal
✅ Partial Cleanup: Handles missing flags gracefully
✅ is_flag_set: Helper function validation
✅ get_flag_value: Value retrieval validation
✅ Convenience Context Managers: Specialized flag patterns
✅ Full Lifecycle: Complete flag creation → cleanup cycle
```

## Benefits Achieved

### 1. **Automatic Cleanup**
- Flags self-destruct after next rerun
- No manual cleanup code required
- Prevents stale flag accumulation

### 2. **Phantom Jump Prevention**
- Eliminates stale flags that cause unexpected stage transitions
- Ensures predictable workflow behavior
- Improves user experience consistency

### 3. **Code Simplification**
- Removes manual flag cleanup logic from app.py
- Reduces boilerplate code in stage files
- Centralizes flag management in one module

### 4. **Debugging Improvement**
- Comprehensive logging of flag operations
- Clear visibility into flag lifecycle
- Debug functions for troubleshooting

### 5. **Maintainability**
- Single source of truth for flag management
- Consistent patterns across all stage transitions
- Easy to add new flag types

## Migration Summary

### Files Modified
- ✅ `utils/flag_helpers.py` - New module with one-shot flag system
- ✅ `app.py` - Added cleanup call and updated flag checking
- ✅ `stages/stage7.py` - Updated 2 flag usage locations
- ✅ `stages/stage8.py` - Updated 1 flag usage location

### Redundant Code Removed
- ✅ Manual `del st.session_state[key]` calls in app.py
- ✅ Explicit flag cleanup logic in stage files
- ✅ Risk of forgotten flag cleanup

### New Capabilities Added
- ✅ Self-destructing flags with automatic cleanup
- ✅ Convenience context managers for common patterns
- ✅ Comprehensive flag debugging utilities
- ✅ Full test coverage with 8 unit tests

The one-shot flag system successfully eliminates the manual flag management burden while preventing stale flag issues that could cause phantom stage jumps in the GretahAI ScriptWeaver workflow.
