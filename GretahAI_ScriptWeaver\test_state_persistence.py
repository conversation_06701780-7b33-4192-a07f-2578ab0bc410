#!/usr/bin/env python3
"""
Test script to verify state persistence between Streamlit reruns.
"""

import sys
import os
sys.path.append('.')

from state_manager import StateManager, StateStage
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('test_state_persistence')

def test_state_persistence():
    """Test that state changes persist correctly."""
    
    print("=== Testing State Persistence ===")
    
    # Create a mock streamlit session state
    class MockST:
        def __init__(self):
            self.session_state = {}

    mock_st = MockST()

    # Test 1: Initialize state manager
    state_manager = StateManager()
    state_manager.init_in_session(mock_st)
    state = StateManager.get(mock_st)

    print(f"Initial state: {state.current_stage.get_display_name()}")
    
    # Test 2: Set up file upload state
    state.uploaded_excel = "test_file.xlsx"
    state.test_cases = [{"Test Case ID": "TC001"}]  # Simulate parsed test cases
    
    print(f"After setting uploaded_excel: {state.uploaded_excel}")
    print(f"After setting test_cases: {len(state.test_cases) if state.test_cases else 0} test cases")
    
    # Test 3: Manually advance to Stage 2
    result = state.advance_to(StateStage.STAGE2_WEBSITE, "Test manual advancement")
    print(f"Manual advancement result: {result}")
    print(f"Current stage after manual advancement: {state.current_stage.get_display_name()}")
    
    # Test 4: Check what update_stage_based_on_completion would do
    print("\nTesting update_stage_based_on_completion...")
    print(f"Before update_stage_based_on_completion: {state.current_stage.get_display_name()}")
    
    # Save current stage
    saved_stage = state.current_stage
    
    # Call update_stage_based_on_completion
    changed = state.update_stage_based_on_completion()
    print(f"update_stage_based_on_completion returned: {changed}")
    print(f"After update_stage_based_on_completion: {state.current_stage.get_display_name()}")
    
    # Test 5: Check if the stage changed unexpectedly
    if state.current_stage != saved_stage:
        print(f"⚠️ WARNING: Stage changed from {saved_stage.get_display_name()} to {state.current_stage.get_display_name()}")
        print("This indicates that update_stage_based_on_completion is overriding manual stage settings")
    else:
        print("✅ Stage remained stable after update_stage_based_on_completion")
    
    # Test 6: Simulate a new session (like a Streamlit rerun)
    print("\nSimulating new session (like Streamlit rerun)...")
    
    # Create a new mock session but with the same session_state
    mock_st2 = MockST()
    mock_st2.session_state = mock_st.session_state  # Preserve session state
    
    # Get the state from the preserved session
    state2 = StateManager.get(mock_st2)
    print(f"State after 'rerun': {state2.current_stage.get_display_name()}")
    print(f"uploaded_excel preserved: {state2.uploaded_excel}")
    print(f"test_cases preserved: {len(state2.test_cases) if state2.test_cases else 0} test cases")
    
    # Test 7: Check if state is the same object
    if state is state2:
        print("✅ State object is preserved between 'reruns'")
    else:
        print("❌ State object is NOT preserved between 'reruns'")
    
    return True

if __name__ == "__main__":
    success = test_state_persistence()
    sys.exit(0 if success else 1)
