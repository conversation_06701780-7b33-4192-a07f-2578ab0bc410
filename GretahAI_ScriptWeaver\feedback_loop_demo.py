#!/usr/bin/env python3
"""
Interactive demonstration of the feedback loop system showing before/after script comparison.
This script simulates the complete feedback loop process and generates detailed comparisons.
"""

import sys
import os
sys.path.append('.')

def create_detailed_comparison():
    """Create a detailed side-by-side comparison with annotations."""
    
    print("🔄 FEEDBACK LOOP SYSTEM DEMONSTRATION")
    print("=" * 80)
    
    # Original script (before feedback)
    original_script = '''import pytest
from selenium import webdriver
from selenium.webdriver.common.by import By

def test_login_functionality(browser):
    """Test login functionality."""
    
    # Navigate to login page
    browser.get("https://example.com/login")
    
    # Find username field using XPath
    username_field = browser.find_element(By.XPATH, "//input[@type='text']")
    username_field.send_keys("testuser")
    
    # Find password field
    password_field = browser.find_element(By.XPATH, "//input[@type='password']")
    password_field.send_keys("password123")
    
    # Click login button
    login_button = browser.find_element(By.XPATH, "//button[contains(text(), 'Login')]")
    login_button.click()
    
    # Check if login was successful
    assert "dashboard" in browser.current_url'''
    
    # Validation feedback
    validation_feedback = {
        'quality_score': 65,
        'issues_found': [
            {'category': 'locators', 'severity': 'medium', 'description': 'Using generic XPath selectors'},
            {'category': 'waits', 'severity': 'high', 'description': 'Missing WebDriverWait conditions'},
            {'category': 'assertions', 'severity': 'medium', 'description': 'Weak assertion without wait'},
            {'category': 'test_data', 'severity': 'low', 'description': 'Hardcoded test data'},
            {'category': 'error_handling', 'severity': 'medium', 'description': 'No error handling'}
        ],
        'recommendations': [
            'Use CSS selectors with specific attributes',
            'Add explicit WebDriverWait conditions',
            'Use more specific assertions with wait conditions',
            'Integrate test_data fixture',
            'Add try/except block with screenshot capture'
        ]
    }
    
    # Regenerated script (after feedback)
    regenerated_script = '''import pytest
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def test_login_functionality(browser, test_data):
    """Test login functionality with enhanced reliability."""
    
    try:
        # Navigate to login page
        browser.get("https://example.com/login")
        
        # Wait for page to load and find username field using CSS selector
        wait = WebDriverWait(browser, 10)
        username_field = wait.until(
            EC.visibility_of_element_located((By.CSS_SELECTOR, "input[name='username']"))
        )
        username_field.send_keys(test_data.get("username", "testuser"))
        
        # Wait for and find password field using CSS selector
        password_field = wait.until(
            EC.visibility_of_element_located((By.CSS_SELECTOR, "input[name='password']"))
        )
        password_field.send_keys(test_data.get("password", "password123"))
        
        # Wait for login button to be clickable and click it
        login_button = wait.until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, "button[data-testid='login-btn']"))
        )
        login_button.click()
        
        # Wait for navigation to complete and verify successful login
        wait.until(EC.url_contains("dashboard"))
        
        # More specific assertion with proper wait
        assert "dashboard" in browser.current_url, f"Expected dashboard URL, got: {browser.current_url}"
        
        # Verify dashboard elements are visible
        dashboard_header = wait.until(
            EC.visibility_of_element_located((By.CSS_SELECTOR, "h1[data-testid='dashboard-title']"))
        )
        assert dashboard_header.is_displayed(), "Dashboard header should be visible after login"
        
    except Exception as e:
        # Capture screenshot on failure for debugging
        browser.save_screenshot(f"login_test_failure_{browser.session_id}.png")
        raise e'''
    
    return original_script, regenerated_script, validation_feedback

def print_side_by_side_comparison(original, regenerated):
    """Print a side-by-side comparison of the scripts."""
    
    original_lines = original.split('\n')
    regenerated_lines = regenerated.split('\n')
    
    max_lines = max(len(original_lines), len(regenerated_lines))
    
    print("\n📊 SIDE-BY-SIDE SCRIPT COMPARISON")
    print("=" * 120)
    print(f"{'ORIGINAL SCRIPT (Before Feedback)':<58} | {'REGENERATED SCRIPT (After Feedback)':<58}")
    print("-" * 58 + " | " + "-" * 58)
    
    for i in range(max_lines):
        original_line = original_lines[i] if i < len(original_lines) else ""
        regenerated_line = regenerated_lines[i] if i < len(regenerated_lines) else ""
        
        # Truncate lines if too long
        if len(original_line) > 55:
            original_line = original_line[:52] + "..."
        if len(regenerated_line) > 55:
            regenerated_line = regenerated_line[:52] + "..."
        
        print(f"{original_line:<58} | {regenerated_line:<58}")

def analyze_specific_improvements(original, regenerated):
    """Analyze and display specific improvements made."""
    
    print("\n🔍 DETAILED IMPROVEMENT ANALYSIS")
    print("=" * 80)
    
    improvements = []
    
    # Locator improvements
    original_xpath = original.count('XPATH')
    regenerated_xpath = regenerated.count('XPATH')
    regenerated_css = regenerated.count('CSS_SELECTOR')
    
    if original_xpath > 0 and regenerated_css > 0:
        improvements.append({
            'category': '🎯 Locator Strategy',
            'before': f'{original_xpath} generic XPath selectors',
            'after': f'{regenerated_css} specific CSS selectors',
            'benefit': 'More reliable and maintainable element identification'
        })
    
    # Wait condition improvements
    original_waits = original.count('WebDriverWait')
    regenerated_waits = regenerated.count('WebDriverWait')
    
    if regenerated_waits > original_waits:
        improvements.append({
            'category': '⏱️ Wait Conditions',
            'before': f'{original_waits} explicit waits',
            'after': f'{regenerated_waits} explicit waits with proper conditions',
            'benefit': 'Eliminates timing issues and improves test stability'
        })
    
    # Test data integration
    if 'test_data' not in original and 'test_data' in regenerated:
        improvements.append({
            'category': '📊 Test Data',
            'before': 'Hardcoded values ("testuser", "password123")',
            'after': 'Dynamic test_data fixture with fallbacks',
            'benefit': 'Flexible test execution with different data sets'
        })
    
    # Error handling
    if 'try:' not in original and 'try:' in regenerated:
        improvements.append({
            'category': '🛡️ Error Handling',
            'before': 'No error handling or debugging support',
            'after': 'Comprehensive try/except with screenshot capture',
            'benefit': 'Better debugging and failure analysis capabilities'
        })
    
    # Assertion improvements
    original_assertions = original.count('assert')
    regenerated_assertions = regenerated.count('assert')
    
    if regenerated_assertions > original_assertions:
        improvements.append({
            'category': '✅ Assertions',
            'before': f'{original_assertions} basic assertion',
            'after': f'{regenerated_assertions} detailed assertions with wait conditions',
            'benefit': 'More thorough validation and better error messages'
        })
    
    # Display improvements
    for i, improvement in enumerate(improvements, 1):
        print(f"\n{i}. {improvement['category']}")
        print(f"   Before: {improvement['before']}")
        print(f"   After:  {improvement['after']}")
        print(f"   Benefit: {improvement['benefit']}")
    
    return improvements

def calculate_quality_metrics(original, regenerated, improvements):
    """Calculate and display quality improvement metrics."""
    
    print("\n📈 QUALITY IMPROVEMENT METRICS")
    print("=" * 80)
    
    # Basic metrics
    original_lines = len(original.split('\n'))
    regenerated_lines = len(regenerated.split('\n'))
    
    # Calculate improvement score
    improvement_score = 0
    for improvement in improvements:
        if 'Locator' in improvement['category']:
            improvement_score += 20
        elif 'Wait' in improvement['category']:
            improvement_score += 25
        elif 'Error' in improvement['category']:
            improvement_score += 20
        elif 'Test Data' in improvement['category']:
            improvement_score += 15
        elif 'Assertions' in improvement['category']:
            improvement_score += 15
    
    print(f"Lines of Code: {original_lines} → {regenerated_lines} ({regenerated_lines - original_lines:+d})")
    print(f"XPath Selectors: {original.count('XPATH')} → {regenerated.count('XPATH')} ({regenerated.count('XPATH') - original.count('XPATH'):+d})")
    print(f"CSS Selectors: {original.count('CSS_SELECTOR')} → {regenerated.count('CSS_SELECTOR')} ({regenerated.count('CSS_SELECTOR'):+d})")
    print(f"Wait Conditions: {original.count('WebDriverWait')} → {regenerated.count('WebDriverWait')} ({regenerated.count('WebDriverWait') - original.count('WebDriverWait'):+d})")
    print(f"Assertions: {original.count('assert')} → {regenerated.count('assert')} ({regenerated.count('assert') - original.count('assert'):+d})")
    print(f"Error Handling: {'No' if 'try:' not in original else 'Yes'} → {'Yes' if 'try:' in regenerated else 'No'}")
    
    print(f"\n🎯 OVERALL IMPROVEMENT")
    print(f"Estimated Quality Score Increase: +{improvement_score} points")
    print(f"Original Quality Score: 65/100")
    print(f"Projected New Quality Score: {min(65 + improvement_score, 100)}/100")
    
    return improvement_score

def main():
    """Run the complete feedback loop demonstration."""
    
    # Create the comparison data
    original, regenerated, feedback = create_detailed_comparison()
    
    # Display validation feedback
    print("\n🔍 VALIDATION FEEDBACK THAT TRIGGERED REGENERATION")
    print("=" * 80)
    print(f"Quality Score: {feedback['quality_score']}/100")
    print(f"Issues Found: {len(feedback['issues_found'])}")
    print(f"Recommendations: {len(feedback['recommendations'])}")
    
    print("\nIssues Identified:")
    for i, issue in enumerate(feedback['issues_found'], 1):
        severity_icon = "🔴" if issue['severity'] == 'high' else "🟡" if issue['severity'] == 'medium' else "🔵"
        print(f"  {i}. {severity_icon} {issue['category'].title()}: {issue['description']}")
    
    print("\nRecommendations Provided:")
    for i, rec in enumerate(feedback['recommendations'], 1):
        print(f"  {i}. {rec}")
    
    # Show side-by-side comparison
    print_side_by_side_comparison(original, regenerated)
    
    # Analyze improvements
    improvements = analyze_specific_improvements(original, regenerated)
    
    # Calculate metrics
    improvement_score = calculate_quality_metrics(original, regenerated, improvements)
    
    # Summary
    print("\n🎉 FEEDBACK LOOP EFFECTIVENESS SUMMARY")
    print("=" * 80)
    print("✅ All validation recommendations were successfully implemented")
    print("✅ Script reliability improved through explicit wait conditions")
    print("✅ Maintainability enhanced with CSS selectors and test data integration")
    print("✅ Debugging capabilities added with error handling and screenshots")
    print("✅ Test coverage improved with additional assertions")
    print(f"✅ Overall quality improvement: +{improvement_score} points")
    
    print("\n💡 This demonstrates how the feedback loop system:")
    print("   • Identifies specific issues in generated scripts")
    print("   • Provides actionable recommendations")
    print("   • Influences regenerated scripts through enhanced prompts")
    print("   • Continuously learns and improves script quality")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
