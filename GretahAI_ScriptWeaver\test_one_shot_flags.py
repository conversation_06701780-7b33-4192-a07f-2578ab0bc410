#!/usr/bin/env python3
"""
Unit tests for one-shot flag functionality in GretahAI ScriptWeaver.

This test suite validates that temporary flags self-destruct after the next rerun,
preventing stale flag issues that can cause phantom stage jumps.
"""

import sys
import os
import logging
from unittest.mock import Mock, patch
import pytest

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MockSessionState:
    """Mock Streamlit session state for testing."""

    def __init__(self):
        self._data = {}

    def __getitem__(self, key):
        return self._data[key]

    def __setitem__(self, key, value):
        self._data[key] = value

    def __delitem__(self, key):
        del self._data[key]

    def __contains__(self, key):
        return key in self._data

    def get(self, key, default=None):
        return self._data.get(key, default)

    def keys(self):
        return self._data.keys()

    def clear(self):
        self._data.clear()


def test_one_shot_flag_basic_functionality():
    """Test that one_shot_flag sets and schedules cleanup correctly."""
    logger.info("Testing basic one-shot flag functionality...")

    # Mock streamlit session state
    mock_session_state = MockSessionState()

    with patch('utils.flag_helpers.st') as mock_st:
        mock_st.session_state = mock_session_state

        from utils.flag_helpers import one_shot_flag

        # Test that flag is set during context
        with one_shot_flag('test_flag'):
            assert 'test_flag' in mock_session_state
            assert mock_session_state['test_flag'] == True

        # Flag should still be present after context exit, cleanup marker should be set
        assert 'test_flag' in mock_session_state
        assert '_cleanup_test_flag' in mock_session_state
        assert mock_session_state['_cleanup_test_flag'] == True

    logger.info("✅ Basic one-shot flag functionality test passed")


def test_one_shot_flag_with_custom_value():
    """Test that one_shot_flag works with custom values."""
    logger.info("Testing one-shot flag with custom value...")

    mock_session_state = MockSessionState()

    with patch('utils.flag_helpers.st') as mock_st:
        mock_st.session_state = mock_session_state

        from utils.flag_helpers import one_shot_flag

        # Test with custom value
        custom_value = {'step': 5, 'data': 'test'}
        with one_shot_flag('custom_flag', custom_value):
            assert mock_session_state['custom_flag'] == custom_value

        # Check cleanup marker after context exit
        assert '_cleanup_custom_flag' in mock_session_state

    logger.info("✅ Custom value one-shot flag test passed")


def test_cleanup_one_shot_flags():
    """Test that cleanup_one_shot_flags removes scheduled flags."""
    logger.info("Testing cleanup_one_shot_flags functionality...")

    mock_session_state = MockSessionState()

    with patch('utils.flag_helpers.st') as mock_st:
        mock_st.session_state = mock_session_state

        from utils.flag_helpers import one_shot_flag, cleanup_one_shot_flags

        # Set up some flags for cleanup
        with one_shot_flag('flag1'):
            pass
        with one_shot_flag('flag2'):
            pass

        # Verify flags are present before cleanup
        assert 'flag1' in mock_session_state
        assert 'flag2' in mock_session_state
        assert '_cleanup_flag1' in mock_session_state
        assert '_cleanup_flag2' in mock_session_state

        # Run cleanup
        cleanup_one_shot_flags()

        # Verify flags are removed after cleanup
        assert 'flag1' not in mock_session_state
        assert 'flag2' not in mock_session_state
        assert '_cleanup_flag1' not in mock_session_state
        assert '_cleanup_flag2' not in mock_session_state

    logger.info("✅ Cleanup one-shot flags test passed")


def test_cleanup_partial_flags():
    """Test cleanup when some flags don't exist."""
    logger.info("Testing cleanup with partial flags...")

    mock_session_state = MockSessionState()

    with patch('utils.flag_helpers.st') as mock_st:
        mock_st.session_state = mock_session_state

        from utils.flag_helpers import cleanup_one_shot_flags

        # Manually set cleanup markers without corresponding flags
        mock_session_state['_cleanup_missing_flag'] = True
        mock_session_state['existing_flag'] = True
        mock_session_state['_cleanup_existing_flag'] = True

        # Run cleanup
        cleanup_one_shot_flags()

        # Verify cleanup markers are removed even if original flags don't exist
        assert '_cleanup_missing_flag' not in mock_session_state
        assert '_cleanup_existing_flag' not in mock_session_state
        assert 'existing_flag' not in mock_session_state

    logger.info("✅ Partial flags cleanup test passed")


def test_is_flag_set():
    """Test the is_flag_set helper function."""
    logger.info("Testing is_flag_set functionality...")

    mock_session_state = MockSessionState()
    mock_session_state['true_flag'] = True
    mock_session_state['false_flag'] = False
    mock_session_state['string_flag'] = 'test'

    with patch('utils.flag_helpers.st') as mock_st:
        mock_st.session_state = mock_session_state

        from utils.flag_helpers import is_flag_set

        assert is_flag_set('true_flag') == True
        assert is_flag_set('false_flag') == False
        assert is_flag_set('string_flag') == 'test'  # Returns the actual value
        assert is_flag_set('missing_flag') == False

    logger.info("✅ is_flag_set test passed")


def test_get_flag_value():
    """Test the get_flag_value helper function."""
    logger.info("Testing get_flag_value functionality...")

    mock_session_state = MockSessionState()
    mock_session_state['test_flag'] = 'test_value'

    with patch('utils.flag_helpers.st') as mock_st:
        mock_st.session_state = mock_session_state

        from utils.flag_helpers import get_flag_value

        assert get_flag_value('test_flag') == 'test_value'
        assert get_flag_value('missing_flag') is None
        assert get_flag_value('missing_flag', 'default') == 'default'

    logger.info("✅ get_flag_value test passed")


def test_convenience_context_managers():
    """Test the convenience context managers."""
    logger.info("Testing convenience context managers...")

    mock_session_state = MockSessionState()

    with patch('utils.flag_helpers.st') as mock_st:
        mock_st.session_state = mock_session_state

        from utils.flag_helpers import coming_from_stage_flag, stage_transition_flag, operation_in_progress_flag

        # Test coming_from_stage_flag
        with coming_from_stage_flag('stage7'):
            assert 'coming_from_stage7' in mock_session_state
        assert '_cleanup_coming_from_stage7' in mock_session_state

        # Test stage_transition_flag
        with stage_transition_flag('stage7', 'stage4'):
            assert 'transitioning_from_stage7_to_stage4' in mock_session_state
        assert '_cleanup_transitioning_from_stage7_to_stage4' in mock_session_state

        # Test operation_in_progress_flag
        with operation_in_progress_flag('optimization'):
            assert 'optimization_in_progress' in mock_session_state
        assert '_cleanup_optimization_in_progress' in mock_session_state

    logger.info("✅ Convenience context managers test passed")


def test_full_lifecycle():
    """Test the complete lifecycle: set flag, rerun, cleanup."""
    logger.info("Testing full flag lifecycle...")

    mock_session_state = MockSessionState()

    with patch('utils.flag_helpers.st') as mock_st:
        mock_st.session_state = mock_session_state

        from utils.flag_helpers import one_shot_flag, cleanup_one_shot_flags, is_flag_set

        # Simulate first run: set flag
        with one_shot_flag('lifecycle_test'):
            # Flag should be present during first run
            assert is_flag_set('lifecycle_test') == True

        # Cleanup marker should be scheduled after context exit
        assert '_cleanup_lifecycle_test' in mock_session_state

        # Simulate second run: flag still present, cleanup scheduled
        assert is_flag_set('lifecycle_test') == True
        assert '_cleanup_lifecycle_test' in mock_session_state

        # Simulate cleanup at start of second run
        cleanup_one_shot_flags()

        # Flag should be gone after cleanup
        assert is_flag_set('lifecycle_test') == False
        assert '_cleanup_lifecycle_test' not in mock_session_state

    logger.info("✅ Full lifecycle test passed")


def run_all_tests():
    """Run all one-shot flag tests."""
    logger.info("=" * 60)
    logger.info("ONE-SHOT FLAG FUNCTIONALITY TESTS")
    logger.info("=" * 60)

    tests = [
        ("Basic Functionality", test_one_shot_flag_basic_functionality),
        ("Custom Value", test_one_shot_flag_with_custom_value),
        ("Cleanup Functionality", test_cleanup_one_shot_flags),
        ("Partial Cleanup", test_cleanup_partial_flags),
        ("is_flag_set", test_is_flag_set),
        ("get_flag_value", test_get_flag_value),
        ("Convenience Context Managers", test_convenience_context_managers),
        ("Full Lifecycle", test_full_lifecycle)
    ]

    passed = 0
    failed = 0

    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} ---")
        try:
            test_func()
            passed += 1
        except Exception as e:
            import traceback
            logger.error(f"❌ {test_name} failed: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            failed += 1

    logger.info("\n" + "=" * 60)
    logger.info("TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    logger.info(f"✅ Passed: {passed}")
    logger.info(f"❌ Failed: {failed}")
    logger.info(f"📊 Total: {passed + failed}")

    if failed == 0:
        logger.info("🎉 All tests passed! One-shot flag functionality is working correctly.")
        return True
    else:
        logger.error(f"⚠️ {failed} test(s) failed. Please review the implementation.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
